import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  Link,
  Field,
  withDatasourceCheck,
  LinkField,
  useSitecoreContext,
  ImageField,
  Image,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { removeURLParams } from 'src/utils/util';
import { faChevronsRight, faChevronUp, faChevronDown } from '@fortawesome/pro-regular-svg-icons';
import {
  faTableList,
  faCircleDollarToSlot,
  faReceipt,
  faPlug,
  faEnvelope,
  faSun,
  faGear,
  faMobileScreen,
  faGlobe,
  faRightFromBracket,
  faChartSimple,
} from '@fortawesome/pro-solid-svg-icons';
import { useAppSelector } from 'src/stores/store';
import { getSwapOrRenewalStatus } from 'src/utils/getSwap';
import { useLoader } from 'src/hooks/modalhooks';
import { getCookie } from 'cookies-next';

type MenuListProps = ComponentProps & {
  fields: {
    Menus: MenuList[];
  };
};

interface MenuList {
  displayName: Field<string>;
  fields: {
    Icon: Field<string>;
    NavText: Field<string>;
    NavLink: LinkField;
    IsHidden: Field<boolean>;
    CssClass: Field<string>;
    HighlightURLs: Field<string>;
    Submenu: MenuList[];
    HideForBusinessUser: Field<boolean>;
    IconImage?: ImageField;
  };
  id: Field<string>;
  name: Field<string>;
  url: Field<string>;
}

function MenuListComponent(props: { menu: MenuList; key: number }) {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  const [isToggled, setIsToggled] = useState(false);
  let isSubToggled = false;
  let currentPath = '';
  let swapOrRenewal = '';
  let isBusinessUser = '';
  let selectedAccount = '';
  let selectedAccountEsiid = '';
  let termMonthCount: number = 0;
  let termUnit: string = '';
  let transferEligibiity: boolean = false;

  const impersonatedCookieValue = getCookie('isImpersonatedUser');
  const impersonatedUser = impersonatedCookieValue === 'true';

  if (!isPageEditing) {
    const router = useRouter();
    currentPath = removeURLParams(router.asPath);
    swapOrRenewal = useAppSelector((state) => state?.authuser?.renewal);
    isBusinessUser = useAppSelector((state) => state?.authuser?.isBusinessUser);
    selectedAccount = useAppSelector(
      (state) => state?.authuser?.accountSelection.contractAccount?.value
    );
    selectedAccountEsiid = useAppSelector(
      (state) => state?.authuser?.accountSelection.esiid?.value
    );
    termUnit = useAppSelector((state) => state.authuser?.termUnit) ?? '';
    termMonthCount = useAppSelector((state) => state?.authuser?.termMonthCount) ?? 0;
    transferEligibiity = useAppSelector((state) => state.authuser?.transferEligibility) ?? false;
  }

  const { openModal, closeAllModal } = useLoader();

  const handleExternalLink = (target: string | undefined) => {
    if (target === '_blank') closeAllModal();
  };

  const Submenus = props.menu.fields.Submenu.map((submenu, submenuindex) => {
    let isSubNavHighlight = false;
    const highlightURLs: string[] = submenu.fields.HighlightURLs.value
      .split(',')
      .map((val) => val.trim());
    highlightURLs.forEach((val) => {
      if (val && currentPath.startsWith(val)) {
        isSubNavHighlight = true;
        isSubToggled = true;
      }
      if (currentPath === submenu.fields.NavLink.value.href) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        isSubNavHighlight = true;
        isSubToggled = true;
      }
    });

    const submenuresponse = getSwapOrRenewalStatus(
      swapOrRenewal?.swapOrRenewalStatus,
      selectedAccount,
      selectedAccountEsiid,
      swapOrRenewal.promo,
      submenu,
      termUnit,
      termMonthCount,
      transferEligibiity,
      impersonatedUser
    );

    if (submenuresponse) submenu.fields.NavLink.value.href = submenuresponse.navLink;

    if (submenu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
    else
      return (
        <li
          key={submenuindex}
          className={`${submenuresponse.allowDisplay ? 'block' : 'hidden'} ${
            submenu.fields.CssClass.value
          }`}
        >
          <Link
            field={submenu.fields.NavLink}
            className={`${
              isSubNavHighlight ? 'text-textPrimary' : 'text-textUndenary'
            } font-primaryRegular hover:text-textSecondary text-minus1`}
            onClick={() => {
              openModal();
              handleExternalLink(props.menu.fields.NavLink?.value?.target);
            }}
          >
            {submenu.fields.NavText.value}
          </Link>
        </li>
      );
  });

  const highlightURLs: string[] = props.menu.fields.HighlightURLs.value
    .split(',')
    .map((val) => val.trim());

  let isNavHighlight = false;
  highlightURLs.forEach((val) => {
    if (val && currentPath.includes(val)) {
      isNavHighlight = true;
    }
  });

  const menuresponse = getSwapOrRenewalStatus(
    swapOrRenewal?.swapOrRenewalStatus,
    selectedAccount,
    selectedAccountEsiid,
    swapOrRenewal.promo,
    props.menu,
    termUnit,
    termMonthCount,
    transferEligibiity,
    impersonatedUser ?? false
  );

  if (menuresponse) props.menu.fields.NavLink.value.href = menuresponse.navLink;
  if (currentPath === props.menu.fields.NavLink.value.href && !isToggled && !isSubToggled) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isNavHighlight = true;
  }

  useEffect(() => {
    if (!isNavHighlight && Submenus.length > 0 && !isSubToggled) {
      setIsToggled(false);
    }
    if (isSubToggled) {
      setIsToggled(true);
    }
  }, [isNavHighlight, isSubToggled]);

  const handleClick = () => {
    isNavHighlight = false;
    setIsToggled((value) => !value);
  };

  const iconMap = {
    'table-list': faTableList,
    'circle-dollar-to-slot': faCircleDollarToSlot,
    'chart-simple': faChartSimple,
    receipt: faReceipt,
    plug: faPlug,
    envelope: faEnvelope,
    sun: faSun,
    gear: faGear,
    'mobile-screen': faMobileScreen,
    globe: faGlobe,
    'right-from-bracket': faRightFromBracket,
  };

  if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
  else
    return (
      <li
        role="menuitem"
        key={props.key}
        className={`${menuresponse.allowDisplay ? 'block' : 'hidden'} ${
          props.menu.fields.CssClass.value
        } mb-1`}
      >
        <div className="flex flex-row items-center">
          <FontAwesomeIcon
            className={`${
              isNavHighlight || isSubToggled ? 'block' : 'hidden'
            } text-textPrimary hidden`}
            icon={faChevronsRight}
          />
          {Submenus.length == 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              aria-expanded="false"
              className={`${
                isNavHighlight
                  ? 'border-solid border-borderSexdenary text-textPrimary'
                  : 'text-textQuattuordenary border-transparent'
              } leading-[24px] cursor-pointer flex items-center border-l-2 hover:text-textSecondary text-minus1  font-primaryRegular border-none gap-3 w-full`}
              field={{ value: props.menu.fields.NavLink.value }}
              onClick={() => {
                openModal();
                handleExternalLink(props.menu.fields.NavLink?.value?.target);
              }}
            >
              {props.menu?.fields?.NavText?.value != '' ? (
                <>
                  {props.menu?.fields?.IconImage?.value?.src ? (
                    <Image
                      className="block w-[16px] h-[16px]"
                      field={props.menu.fields.IconImage}
                    />
                  ) : props.menu?.fields?.Icon?.value ? (
                    <FontAwesomeIcon
                      className="block w-[16px]"
                      icon={
                        iconMap[props.menu.fields.Icon.value as keyof typeof iconMap] ||
                        faChevronsRight
                      }
                    />
                  ) : null}

                  <span className="my-account-showMenu relative">
                    {props.menu.fields.NavText.value}
                  </span>
                </>
              ) : null}
            </Link>
          )}
          {Submenus.length > 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              aria-expanded="true"
              role="button"
              className={`${
                isNavHighlight || isSubToggled
                  ? 'border-solid border-textSexdenary text-textPrimary'
                  : 'text-textQuattuordenary border-transparent'
              } leading-[24px] cursor-pointer flex items-center border-l-2 hover:text-textSecondary text-minus1 ml-0 pl-0  font-primaryRegular border-none gap-3 w-full`}
              field={{ value: props.menu.fields.NavLink.value }}
              onClick={(e) => {
                e.preventDefault();
                handleClick();
              }}
            >
              {props.menu?.fields?.IconImage?.value?.src ? (
                <Image className="block w-[16px] h-[16px]" field={props.menu.fields.IconImage} />
              ) : (
                <FontAwesomeIcon
                  className="block w-[16px]"
                  icon={
                    iconMap[props.menu?.fields?.Icon?.value as keyof typeof iconMap] ||
                    faChevronsRight
                  }
                />
              )}
              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}
          {Submenus.length > 0 && (
            <FontAwesomeIcon
              className="ml-auto mr-1 cursor-pointer text-textSecondary"
              icon={isToggled ? faChevronUp : faChevronDown}
              onClick={() => handleClick()}
            />
          )}
        </div>
        {isToggled && (
          <div className="flex flex-row gap-3 py-3 ml-7 pl-0">
            <div className="border-l-2 border-borderOctonary hidden"></div>
            <ul className="flex flex-col gap-3">{Submenus}</ul>
          </div>
        )}
      </li>
    );
}

const MenuList = (props: MenuListProps): JSX.Element | null => {
  const menuList: MenuList[] = props.fields.Menus;

  const MenuLinks = menuList.map((menu, index) => <MenuListComponent menu={menu} key={index} />);

  return (
    <div className="">
      <div className="hidden md:block wide:hidden ipad:hidden">
        {/* menu */}
        <nav aria-label="Main navigation">
          <ul role="menu" className="grid gap-2 sm:px-5 sm:ml-2 sm:pb-3">
            {MenuLinks}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export { MenuList };
const Component = withDatasourceCheck()<MenuListProps>(MenuList);
export default aiLogger(Component, Component.name);
