import {
  faCircle<PERSON>ser,
  faChevronUp,
  faChevronDown,
  faChevronsRight,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Popover, UnstyledButton } from '@mantine/core';
import {
  Text,
  Field,
  withDatasourceCheck,
  Link,
  LinkField,
  Placeholder,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import LanguageSelectorMobile from 'components/common/LanguageSelectorMobile/LanguageSelectorMobile';

import { getCookie } from 'cookies-next';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import { useAppSelector } from 'src/stores/store';
import { getSwapOrRenewalStatus } from 'src/utils/getSwap';
import { removeURLParams } from 'src/utils/util';

type MobileMenuProps = ComponentProps & {
  fields: {
    Menus: MenuList[];
    WelcomeText: Field<string>;
    MyAccountText: Field<string>;
  };
};

interface MenuList {
  displayName: Field<string>;
  fields: {
    NavText: Field<string>;
    NavLink: LinkField;
    IsHidden: Field<boolean>;
    CssClass: Field<string>;
    MobileCssClass: Field<string>;
    HighlightURLs: Field<string>;
    Submenu: MenuList[];
    HideForBusinessUser: Field<boolean>;
  };
  id: Field<string>;
  name: Field<string>;
  url: Field<string>;
}

function MenuListComponent(props: { menu: MenuList; key: number }) {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  const [isToggled, setIsToggled] = useState(false);
  const isBusinessUser = useAppSelector((state) => state.authuser?.isBusinessUser);
  let isSubToggled = false;
  const router = useRouter();
  const currentPath = removeURLParams(router.asPath);
  const swapOrRenewal = useAppSelector((state) => state.authuser?.renewal);
  const { openModal } = useLoader();
  let transferEligibiity: boolean = false;
  let termUnit: string = '';
  let termMonthCount: number = 0;
  let impersonatedUser: boolean = false;

  const selectedAccount = useAppSelector(
    (state) => state.authuser?.accountSelection.contractAccount?.value
  );
  const selectedAccountEsiid = useAppSelector(
    (state) => state.authuser?.accountSelection.esiid?.value
  );
  const Submenus = props.menu.fields.Submenu.map((submenu, submenuindex) => {
    let isSubNavHighlight = false;
    const highlightURLs: string[] = submenu.fields.HighlightURLs.value
      .split(',')
      .map((val) => val.trim());
    highlightURLs.forEach((val) => {
      if (val && currentPath.startsWith(val)) {
        isSubNavHighlight = true;
      }
      if (currentPath === submenu.fields.NavLink.value.href) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        isSubNavHighlight = true;
        isSubToggled = true;
      }
    });
    if (!isPageEditing) {
      termUnit = useAppSelector((state) => state.authuser?.termUnit) ?? '';
      termMonthCount = useAppSelector((state) => state?.authuser?.termMonthCount) ?? 0;
      transferEligibiity = useAppSelector((state) => state.authuser?.transferEligibility) ?? false;
      const impersonatedCookieValue = getCookie('isImpersonatedUser');
      impersonatedUser = impersonatedCookieValue === 'true';
    }

    const submenuresponse = getSwapOrRenewalStatus(
      swapOrRenewal?.swapOrRenewalStatus ?? '',
      selectedAccount ?? '',
      selectedAccountEsiid ?? '',
      swapOrRenewal?.promo ?? '',
      submenu,
      termUnit,
      termMonthCount,
      transferEligibiity,
      impersonatedUser
    );

    if (submenuresponse) submenu.fields.NavLink.value.href = submenuresponse.navLink;

    if (submenu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
    else
      return (
        <li
          key={submenuindex}
          className={`${submenuresponse.allowDisplay ? 'block' : 'hidden'} ${
            submenu.fields.MobileCssClass.value
          }`}
        >
          <Link
            field={submenu.fields.NavLink}
            className={`${
              isSubNavHighlight ? 'text-textPrimary' : 'text-textPrimary'
            } font-primaryRegular text-minus2 hover:text-textSecondary`}
            onClick={() => openModal()}
          >
            {submenu.fields.NavText.value}
          </Link>
        </li>
      );
  });

  const highlightURLs: string[] = props.menu.fields.HighlightURLs.value
    .split(',')
    .map((val) => val.trim());

  let isNavHighlight = false;
  highlightURLs.forEach((val) => {
    if (val && currentPath.includes(val)) {
      isNavHighlight = true;
    }
  });

  const menuresponse = getSwapOrRenewalStatus(
    swapOrRenewal?.swapOrRenewalStatus ?? '',
    selectedAccount ?? '',
    selectedAccountEsiid ?? '',
    swapOrRenewal?.promo ?? '',
    props.menu,
    termUnit,
    termMonthCount,
    transferEligibiity,
    impersonatedUser ?? false
  );

  if (menuresponse) props.menu.fields.NavLink.value.href = menuresponse.navLink;
  if (currentPath === props.menu.fields.NavLink.value.href && !isToggled && !isSubToggled) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    isNavHighlight = true;
  }

  useEffect(() => {
    if (!isNavHighlight && Submenus.length > 0 && !isSubToggled) {
      setIsToggled(false);
    }
    if (isSubToggled) {
      setIsToggled(true);
    }
  }, [isNavHighlight, isSubToggled]);

  const handleClick = () => {
    isNavHighlight = false;
    setIsToggled((value) => !value);
  };

  if (props.menu.fields.HideForBusinessUser?.value === true && isBusinessUser) return null;
  else
    return (
      <li
        key={props.key}
        className={`${menuresponse.allowDisplay ? 'block' : 'hidden'} ${
          props.menu.fields.MobileCssClass.value
        }`}
      >
        <div className="flex flex-row items-center">
          <FontAwesomeIcon
            className={`${
              isNavHighlight || isSubToggled ? 'hidden' : 'hidden'
            } text-textSecondary text-minus2`}
            icon={faChevronsRight}
          />
          {Submenus.length == 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${
                isNavHighlight
                  ? 'text-textPrimary border-none font-primaryBold'
                  : 'text-textPrimary border-transparent font-primaryRegular ml-4'
              } leading-[24px] cursor-pointer flex gap-2 pl-2 border-l-2 border-solid text-textPrimary hover:text-textSecondary text-minus2`}
              field={{ value: props.menu.fields.NavLink.value }}
              role="menuitem"
              onClick={() => openModal()}
            >
              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}
          {Submenus.length > 0 && (
            <Link
              aria-current={isNavHighlight ? 'page' : undefined}
              className={`${
                isNavHighlight || isSubToggled
                  ? 'text-textPrimary border-none font-primaryBold'
                  : 'text-textPrimary border-transparent font-primaryRegular ml-4'
              } leading-[24px] cursor-pointer flex gap-2 pl-2 border-l-2 border-solid text-textUndenary hover:text-textSecondary text-minus2`}
              field={{ value: props.menu.fields.NavLink.value }}
              onClick={(e) => {
                e.preventDefault();
                handleClick();
              }}
              role="menuitem"
            >
              <span className="my-account-showMenu relative">
                {props.menu.fields.NavText.value}
              </span>
            </Link>
          )}
          {Submenus.length > 0 && (
            <FontAwesomeIcon
              className="ml-auto mr-1 cursor-pointer text-textPrimary"
              icon={isToggled ? faChevronUp : faChevronDown}
              onClick={() => handleClick()}
            />
          )}
        </div>
        {isToggled && (
          <div className="flex flex-row ml-8 gap-3 py-3">
            <div className="border-l-2 border-borderOctonary hidden"></div>
            <ul className="flex flex-col gap-3">{Submenus}</ul>
          </div>
        )}
      </li>
    );
}

const MobileMenu = (props: MobileMenuProps): JSX.Element | null => {
  //const [activeLink, setActiveLink] = useState('');
  const [showMenu, setShowMenu] = useState(false);
  const custFName = getCookie('customer_name');
  const [firstName] = useState(custFName !== undefined ? custFName : '');
  const menuList: MenuList[] = props.fields.Menus;
  const router = useRouter();

  //TODO: Change to props in the child component
  useEffect(() => {
    setShowMenu(false);
  }, [router]);

  const MenuLinks = menuList.map((menu, index) => <MenuListComponent menu={menu} key={index} />);
  return (
    <div className="ml-auto">
      {/* mobile menu */}
      <div className="md:hidden ml-auto wide:block ipad:block mr-[10px]">
        <Popover opened={showMenu} width={260} position="bottom-start" trapFocus>
          <Popover.Target>
            <UnstyledButton
              className="text-textPrimary text-base font-primaryBlack flex flex-row items-center"
              onClick={() => setShowMenu(!showMenu)}
            >
              <FontAwesomeIcon icon={faCircleUser} className="text-textPrimary" />
              <div className="pl-[15px] flex flex-row">
                {firstName === '' ? (
                  <div>Loading...</div>
                ) : (
                  <Text
                    tag="p"
                    className="text-textQuattuordenary font-primaryRegular text-minus2 leading-[24px] tracking-[-0.25px] uppercase"
                    field={{ value: props.fields.WelcomeText.value + ' ' + firstName }}
                  />
                )}
                <div className="flex items-center">
                  <Text
                    tag="p"
                    className="text-textPrimary font-primaryRegular text-base leading-[22px]"
                    field={{ value: props.fields.MyAccountText.value }}
                  />
                  {showMenu ? (
                    <FontAwesomeIcon
                      icon={faChevronUp}
                      className="pl-[4px] text-textPrimary h-[15px] w-[15px] border-solid"
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faChevronDown}
                      className="pl-[4px] text-textPrimary h-[15px] w-[15px] border-solid"
                    />
                  )}
                </div>
              </div>
            </UnstyledButton>
          </Popover.Target>
          <Popover.Dropdown className="!fixed px-4 pt-2 pb-6 block min-w-[228px] mt-[20px] h-full right-0 left-[auto!important] top-[50px] ipad:top-[85px!important] w-[100%!important] shadow-3xl rounded-none">
            {/* menu */}
            <nav aria-label="Main navigation">
              <ul role="menu" className="grid gap-2">
                {MenuLinks}
              </ul>
              <div>
                <LanguageSelectorMobile />
              </div>
              <div>
                <Placeholder
                  name="jss-mobilechat"
                  rendering={props.rendering}
                  render={(components) => <div className="">{components}</div>}
                />
              </div>
            </nav>
          </Popover.Dropdown>
        </Popover>
      </div>
    </div>
  );
};
export { MobileMenu };
const Component = withDatasourceCheck()<MobileMenuProps>(MobileMenu);
export default aiLogger(Component, Component.name);
