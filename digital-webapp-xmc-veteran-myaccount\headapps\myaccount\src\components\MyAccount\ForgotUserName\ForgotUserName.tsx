import { faArrowRight, faChevronsRight, faSpinner } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Radio, TextInput, UnstyledButton } from '@mantine/core';
import Button from 'components/Elements/Button/Button';
import { useForm, zodResolver } from '@mantine/form';
import {
  Text,
  Field,
  withDatasourceCheck,
  LinkField,
  RichText,
  ImageField,
  Image,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import { useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { z } from 'zod';
import axios from 'axios';
import { ForgotUserNameResponse } from 'src/services/AuthenticationAPI/types';
import { useRouter } from 'next/router';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type ForgotUserNameProps = ComponentProps & {
  fields: {
    RecoveryEmailTitle: Field<string>;
    RecoveryEmailDescription: Field<string>;
    EmailAddressPlaceholder: Field<string>;
    SubmitButtonLabel: Field<string>;
    CancelButtonLabel: Field<string>;
    LoginPageUrl: LinkField;
    ThumbsUpIcon: ImageField;
    EmailSentText: Field<string>;
    EmailSentDescriptionText: Field<string>;
    BackToLoginLabel: Field<string>;
    EmailValidationMessage: Field<string>;
    MultipleAccountsErrorMessage: Field<string>;
    EmailRadioButtonText: Field<string>;
    AccountNumberButtonText: Field<string>;
    CoummunicationEmailLabel: Field<string>;
    ContractAccountLabel: Field<string>;
    ContractAccountValidationMessage: Field<string>;
    InvalidAccountNumberMessage: Field<string>;
  };
};

const ForgotUserName = (props: ForgotUserNameProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="ForgotUserName" />;

  const [loading, setLoading] = useState(false);
  const [userNameExists, setUserNameExists] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [selectedMethod, setSelectedMethod] = useState<string>('2');
  const handleRadioChange = (value: string) => {
    setSelectedMethod(value);
    if (value === '1') {
      form.setFieldValue('AccountNumber', '');
    } else if (value === '2') {
      form.setFieldValue('EmailAddress', '');
    }
  };

  const router = useRouter();

  const schema = z
    .object({
      EmailAddress: z.string().optional(),
      AccountNumber: z.string().optional(),
    })
    .superRefine((data, ctx) => {
      if (selectedMethod === '1') {
        if (!data.EmailAddress) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.EmailValidationMessage.value,
            path: ['EmailAddress'],
          });
        } else if (!z.string().email().safeParse(data.EmailAddress).success) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.EmailValidationMessage.value,
            path: ['EmailAddress'],
          });
        }
      }

      if (selectedMethod === '2') {
        if (!data.AccountNumber) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.ContractAccountValidationMessage.value,
            path: ['AccountNumber'],
          });
        } else if (!/^\d+$/.test(data.AccountNumber)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.fields.InvalidAccountNumberMessage.value,
            path: ['AccountNumber'],
          });
        }
      }
    });

  const form = useForm({
    initialValues: {
      EmailAddress: '',
      AccountNumber: '',
    },
    validate: zodResolver(schema),
    validateInputOnBlur: true,
  });

  const forgotusername = async (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();

    form.validate();

    if (form.isValid()) {
      setLoading(true);

      const payload =
        selectedMethod === '1'
          ? { EmailAddress: form.values.EmailAddress, AccountNumber: '' }
          : { EmailAddress: '', AccountNumber: form.values.AccountNumber };


      try {
        const req = await axios.post<ForgotUserNameResponse>('/api/forgotusername', payload);

        if (req.data.result) {
          setLoading(false);
          setUserNameExists(true);
        } else if (!req.data.result && req.data.hasErrors) {
          setLoading(false);
          setUserNameExists(false);
          if (req.data.messages[0] === 'Multiple Accounts Error Message') {
            setErrorMessage(props.fields.MultipleAccountsErrorMessage.value);
          } else {
            setErrorMessage(req.data.messages[0]);
          }
        } else if (!req.data.result && !req.data.hasErrors) {
          setLoading(false);
          setUserNameExists(false);
        }
      } catch (err) {
        console.error('API error :::', err);
        setLoading(false);
      }
    }
  };

  const redirectBackToLoginPage = async () => {
    router.push({
      pathname: props.fields.LoginPageUrl.value.href,
    });
  };

  return (
    <div className="w-full flex flex-col items-start sm:mb-[14rem] mb-4 sm:mt-4 mt-[7rem]">
      <div className="flex flex-col md:gap-5 items-center md:items-start w-full px-4 lg:px-0 md:max-w-[880px] md:w-full gap-8 justify-center my-0 mx-auto">
        {!userNameExists && (
          <>
            <div className="w-full sm:max-w-[620px]">
              <Text
                tag="p"
                field={{ value: props.fields.RecoveryEmailTitle.value }}
                className="font-primaryBlack text-textUndenary sm:text-plus3 text-plus1 pb-4"
              />
              <Text
                tag="p"
                field={{
                  value: props.fields.RecoveryEmailDescription.value,
                }}
                className="text-textQuattuordenary sm:text-minus1 font-primaryRegular pb-4"
              />
            </div>

            <form className="w-full flex flex-col items-center sm:items-start gap-6 sm:max-w-[455px]">
              <Radio className='hidden'
                value={'1'}
                label={props.fields.EmailRadioButtonText.value}
                checked={selectedMethod === '1'}
                onChange={() => handleRadioChange('1')}
                styles={{
                  root: {
                    marginTop: '-10px',
                  },
                }}
              />

              {selectedMethod === '1' && (
                <TextInput
                  className=""
                  label={props.fields.CoummunicationEmailLabel.value}
                  // placeholder={props.fields.EmailAddressPlaceholder.value}
                  styles={{
                    root: {
                      width: '100%',
                      ['@media (min-width: 1024px)']: {
                        width: '100%',
                      },
                    },
                  }}
                  withAsterisk
                  {...form.getInputProps('EmailAddress')}
                />
              )}
              <Radio
                value={'2'}
                label={props.fields.AccountNumberButtonText.value}
                checked={selectedMethod === '2'}
                onChange={() => handleRadioChange('2')}
                styles={{
                  root: {
                    marginTop: '-10px',
                  },
                }}
              />
              {selectedMethod === '2' && (
                <TextInput
                  className=""
                  label={props.fields.ContractAccountLabel.value}
                  // placeholder={props.fields.EmailAddressPlaceholder.value}
                  styles={{
                    root: {
                      width: '100%',
                      ['@media (min-width: 1024px)']: {
                        width: '100%',
                      },
                    },
                  }}
                  withAsterisk
                  {...form.getInputProps('AccountNumber')}
                />
              )}

              <div className="flex flex-row gap-6 items-center jutify-center sm:justify-start">
                <Button
                  className="w-[160px]"
                  icon={
                    loading ? (
                      <FontAwesomeIcon
                        icon={faSpinner}
                        className="text-textQuinary"
                        size="xs"
                        spin
                      />
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faArrowRight} className="hidden" />
                        <FontAwesomeIcon icon={faChevronsRight} className="hidden text-minus2" />
                      </>
                    )
                  }
                  disabled={loading}
                  onClick={forgotusername}
                >
                  {props.fields.SubmitButtonLabel.value}
                </Button>
                <UnstyledButton
                  className="text-textPrimary hover:text-textSecondary sm:text-plus1 text-minus1 font-primaryBold decoration-2 decoration-textPrimary hover:decoration-textSecondary"
                  onClick={redirectBackToLoginPage}
                >
                  {props.fields.CancelButtonLabel.value}
                </UnstyledButton>
              </div>
              {!userNameExists && errorMessage && errorMessage.length > 0 && (
                <div className="">
                  <RichText
                    tag="p"
                    className="font-primaryBold text-minus1 text-textDenary"
                    field={{
                      value: Array.isArray(errorMessage)
                        ? errorMessage.join(' ')
                        : errorMessage || '',
                    }}
                  />
                </div>
              )}
            </form>
          </>
        )}

        {userNameExists && (
          <>
            <div className="flex flex-col items-center gap-8">
              <Image className="w-full max-w-[75px]" field={props.fields.ThumbsUpIcon} />
              <RichText
                tag="p"
                field={{ value: props.fields.EmailSentText.value }}
                className="sm:text-plus3 text-plus2 font-primaryBold text-textPrimary"
              />
              <RichText
                tag="p"
                field={{ value: props.fields.EmailSentDescriptionText.value }}
                className="sm:text-minus1 text-minus3 font-primaryRegular text-textQuattuordenary"
              />
              <Button
                className="w-[214px] px-2 mt-6"
                icon={
                  loading ? (
                    <FontAwesomeIcon icon={faSpinner} className="text-textQuinary" size="xs" spin />
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faArrowRight} className="" />
                      <FontAwesomeIcon icon={faChevronsRight} className="hidden text-minus2" />
                    </>
                  )
                }
                disabled={loading}
                onClick={redirectBackToLoginPage}
              >
                {props.fields.BackToLoginLabel.value}
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export { ForgotUserName };
const Component = withDatasourceCheck()<ForgotUserNameProps>(ForgotUserName);
export default aiLogger(Component, Component.name);
