import { faBuildingColumns, faChevronRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { UnstyledButton } from '@mantine/core';
import {
  Field,
  LinkField,
  Text,
  useSitecoreContext,
  withDatasourceCheck,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { GetPaymentMethodsResponse } from 'src/services/MyAccountAPI/types';
import { setBankEditDetails, setCardEditDetails } from 'src/stores/paymentSlice';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { getCardIcon } from 'src/utils/cardHelper';
import Loader from 'components/common/Loader/Loader';
import { isExpired, isExpiring } from 'src/utils/getCardExpiration';
import React, { useEffect, useState } from 'react';
import { GetUserProfileResponse } from 'src/services/AuthenticationAPI/types';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type ManagePaymentMethodsProps = ComponentProps & {
  fields: {
    data: {
      item: {
        PaymentMethodsTitleText: Field<string>;
        CardEndingText: Field<string>;
        AccountEndingText: Field<string>;
        AddCardBtnText: Field<string>;
        AddBankAccountBtnText: Field<string>;
        EditCardLink?: { jsonValue: LinkField };
        EditBankLink?: { jsonValue: LinkField };
        InvalidExpirationDateError: Field<string>;
        ExpireUpdateCardText: Field<string>;
        CardExpiringSoonError: Field<string>;
        AddPaymentMethodText: Field<string>;
        AutoPayEnableLabel?: { jsonValue: { value: string } };
      };
    };
  };
  openAddBankModal: () => void;
  openAddCardModal: () => void;
  isPaymentAutopayModify?: boolean;
  updateAutopayModify?: (x: boolean) => void;
};

const branding = process.env.NEXT_PUBLIC_SITE_NAME;

const ManagePaymentMethods = (props: ManagePaymentMethodsProps): JSX.Element => {
  console.log('props=managepaymentmethods=', props);
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="ManagePaymentMethods" />;

  const [userProfileData, setUserProfileData] = useState<GetUserProfileResponse>();
  const dispatch = useAppDispatch();
  const selectedAccount = useAppSelector(
    (state) => state.authuser.accountSelection.contractAccount?.value
  );

  const { isLoading, data, error, refetch } = useQuery({
    queryKey: ['paymentmethods'],
    queryFn: () =>
      axios
        .get<GetPaymentMethodsResponse>('/api/myaccount/payments/paymentmethods', {
          params: {
            accountNumber: selectedAccount,
          },
        })
        .then((res) => res.data),
    enabled: !!selectedAccount,
  });
  const router = useRouter();
  if (error) return <></>;

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    async function getUserProfileInfo() {
      const req = await axios
        .get<GetUserProfileResponse>('/api/userprofile')
        .then((res) => res.data);

      if (req) {
        console.log(req.result);
        if (req?.result?.isFraud) {
          console.log('Customer is Fraud!!');
          router.push('/oops-payment');
        }
        setUserProfileData(req);
      }
    }
    getUserProfileInfo();
  }, []);

  useEffect(() => {
    if (props.isPaymentAutopayModify) {
      refetch();
      if (props.updateAutopayModify) props.updateAutopayModify(false);
    }
  }, [props]);

  const Wrapper = 'div';
  console.log(props, '--');

  if (isLoading)
    return (
      <div>
        <div className="text-plus2 font-primaryBold text-textUndenary">
          <h2>{props?.fields?.data?.item?.PaymentMethodsTitleText?.value}</h2>
        </div>
        {/* CREATE CARDS: DISPLAY COLLECTION OF CARDS */}
        <div className="mt-7">
          <div className="sm:shadow-3xl sm:p-5 sm:rounded-xl m-6 sm:m-0 max-w-[592px] flex flex-col gap-5 shadow-none">
            <Text
              tag="p"
              field={{ value: props?.fields?.data?.item?.PaymentMethodsTitleText.value }}
              className="font-primaryBlack hidden text-base text-textPrimary sm:text-plus2"
            />
            <Loader />
          </div>
        </div>
        {/* Buttons should not be visible on loading */}
        {/* <div className="flex font-mProBlack tee:font-gProBold space-between pt-16 pb-5">
          <Button type="button" className="">
            {props?.fields?.data?.item?.AddCardBtnText.value}
          </Button>

          {!userProfileData?.result?.isCashOnly && !userProfileData?.result?.isFraud && (
            <Button type="button" className="ml-8">
              {props?.fields?.data?.item?.AddBankAccountBtnText.value}
            </Button>
          )}
        </div> */}
      </div>
    );

  return (
  <div className="flex flex-col gap-5 bg-gray-100 rounded-lg p-5 sm:w-4/5">
      <div className="font-primaryBlack flex justify-start lg:justify-start">
        <h2 className="text-plus1 sm:text-plus2 font-primaryBold text-textUndenary">
          {props?.fields?.data?.item?.PaymentMethodsTitleText?.value}
        </h2>
      </div>
      {/* CREATE CARDS: DISPLAY COLLECTION OF CARDS */}
      <div className="w-full rounded-xl flex flex-col items-center gap-5 lg:items-start border-hide shadow-none p-0  sm:max-w-[640px] sm:grid sm:grid-cols-1">
        <Text
          tag="p"
          field={{ value: props?.fields?.data?.item?.PaymentMethodsTitleText.value }}
          className="font-primaryBlack hidden text-base text-textPrimary sm:text-plus2"
        />
        <div className="border-b-[2px] border-b-borderQuattuordenary w-full hidden" />
        {data?.result.bankList.map((bank) => {
          return (
            <>
              <div
                key={bank.AccountId}
                onClick={() => {
                  dispatch(
                    setBankEditDetails({
                      nickname: bank.Nickname,
                      accountholderName: bank.HoldersName,
                      routingNumber: bank.RoutingNumber,
                      bankDisplayNumber: bank.DisplayAccountNumber,
                      accountId: bank.AccountId,
                      profileId: bank.profileId,
                      hasScheduledPayments: bank.HasScheduledPayments,
                      hasRecurringPayments: bank.HasRecurringPayments,
                    })
                  );

                  router.push({
                    pathname: props?.fields?.data?.item?.EditBankLink?.jsonValue?.value?.href,
                  });
                }}
                className="border-[1.5px] border-solid border-[#87858E] p-3 rounded-[4px] w-full"
              >
                {/* bank account item */}
                <div className="flex flex-row items-center w-full gap-4 cursor-pointer">
                  <div className="w-[36px] h-[28px] flex items-center justify-center rounded-[4px] bg-bgSecondary ml-2">
                    <FontAwesomeIcon icon={faBuildingColumns} className="text-textQuinary" />
                  </div>
                  <div className="flex flex-grow overflow-hidden flex-col w-full">
                    <Text
                      tag="p"
                      field={{ value: bank.Nickname }}
                      className="font-primaryBold text-textUndenary text-minus2 sm:text-base sm:basis-2/4"
                    />

                    <Text
                      tag="p"
                      field={{
                        value: props?.fields?.data?.item?.AccountEndingText.value.replace(
                          '{AccountNumber}',
                          `${bank.DisplayAccountNumber}`
                        ),
                      }}
                      className="sm:text-minus2 sm:basis-2/4 font-primaryRegular text-minus2 text-textUndenary"
                    />
                  </div>

                  <UnstyledButton>
                    <FontAwesomeIcon
                      icon={faChevronRight}
                      className="relative top-[2px] text-plus2 text-textPrimary hover:text-textSecondary"
                    />
                  </UnstyledButton>
                </div>
                {bank.HasRecurringPayments && (
                  <Text
                    className="tee-custom-color font-primaryBold relative left-4 text-textPrimary text-xs md:ml-0 md:text-sm pt-1"
                    tag="p"
                    field={{
                      value: props.fields?.data?.item?.AutoPayEnableLabel?.jsonValue?.value,
                    }}
                  ></Text>
                )}
              </div>
            </>
          );
        })}
        {data?.result.cardList.map((card) => {
          return (
            <Wrapper key={card.AccountId} className="w-full">
              <div
                key={card.AccountId}
                onClick={() => {
                  dispatch(
                    setCardEditDetails({
                      cardholderName: card.HoldersName,
                      cardDisplayNumber: card.DisplayAccountNumber,
                      expiration: card.Expiration,
                      zipCode: card.BillingPostalCode,
                      nickname: card.Nickname,
                      accountId: card.AccountId,
                      profileId: card.profileId,
                      hasSchedulePayments: card.HasScheduledPayments,
                      hasRecurringPayments: card.HasRecurringPayments,
                      cardBrand: card.CardBrand,
                    })
                  );

                  router.push({
                    pathname: props?.fields?.data?.item?.EditCardLink?.jsonValue?.value.href,
                  });
                }}
                className="border-[1.5px] border-solid border-[#87858E] rounded-[4px]  p-3 "
              >
                <div className="flex flex-row items-center w-full gap-3 cursor-pointer">
                  <FontAwesomeIcon
                    icon={getCardIcon(card.AccountId)}
                    className="w-[48px] h-[32px] text-textSecondary"
                  />
                  <div className="flex flex-grow overflow-hidden flex-col w-full">
                    <Text
                      tag="p"
                      field={{ value: card.Nickname }}
                      className="lg:text-base sm:basis-2/4 font-primaryBold text-textUndenary text-minus2 whitespace-nowrap"
                    />
                    <Text
                      tag="p"
                      field={{
                        value: card.Nickname,
                      }}
                      className="font-primaryRegular text-textQuattuordenary text-minus3 sm:text-minus2 sm:basis-2/4 hidden"
                    />
                    <Text
                      tag="p"
                      field={{
                        value: props?.fields?.data?.item?.CardEndingText.value.replace(
                          '{CardNumber}',
                          `${card.DisplayAccountNumber}`
                        ),
                      }}
                      className="sm:text-minus2 sm:basis-2/4 font-primaryRegular text-textUndenary text-minus2"
                    />
                  </div>

                  <UnstyledButton>
                    <FontAwesomeIcon
                      icon={faChevronRight}
                      className="text-textPrimary hover:text-textSecondary text-plus2"
                    />
                  </UnstyledButton>
                </div>
                {card.HasRecurringPayments && (
                  <Text
                    className="tee-custom-color font-primaryBold text-textPrimary text-xs md:ml-0 md:text-sm pt-1"
                    tag="p"
                    field={{
                      value: props.fields?.data?.item?.AutoPayEnableLabel?.jsonValue?.value,
                    }}
                  ></Text>
                )}
              </div>
              {/* <div className="border-b-[2px] border-b-charcoal-25 w-full tee:hidden" /> */}
              {/* Check if the card is expired */}
              {isExpired(card.Expiration) && (
                <div>
                  <div>
                    <div>
                      <p className="font-primaryRegular mt-2 text-minus2">
                        <span className="text-textDenary">
                          {props?.fields?.data?.item?.InvalidExpirationDateError?.value}
                        </span>
                        <UnstyledButton
                          className="font-primaryBold text-textPrimary text-minus2"
                          onClick={() => {
                            dispatch(
                              setCardEditDetails({
                                cardholderName: card.HoldersName,
                                cardDisplayNumber: card.DisplayAccountNumber,
                                expiration: card.Expiration,
                                zipCode: card.BillingPostalCode,
                                nickname: card.Nickname,
                                accountId: card.AccountId,
                                profileId: card.profileId,
                                hasSchedulePayments: card.HasScheduledPayments,
                                hasRecurringPayments: card.HasRecurringPayments,
                                cardBrand: card.CardBrand,
                              })
                            );

                            router.push({
                              pathname:
                                props?.fields?.data?.item?.EditCardLink?.jsonValue?.value.href,
                            });
                          }}
                        >
                          {props?.fields?.data?.item?.ExpireUpdateCardText?.value}
                        </UnstyledButton>
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Check if the card is isExpiring */}
              {isExpiring(card.Expiration) && (
                <div>
                  <div>
                    <div>
                      <p className="text-minus2 font-primaryRegular mt-2">
                        <span className="text-textDenary">
                          {props?.fields?.data?.item?.CardExpiringSoonError?.value}{' '}
                        </span>
                        <UnstyledButton
                          className="font-primaryBold text-textPrimary text-minus2"
                          onClick={() => {
                            dispatch(
                              setCardEditDetails({
                                cardholderName: card.HoldersName,
                                cardDisplayNumber: card.DisplayAccountNumber,
                                expiration: card.Expiration,
                                zipCode: card.BillingPostalCode,
                                nickname: card.Nickname,
                                accountId: card.AccountId,
                                profileId: card.profileId,
                                hasSchedulePayments: card.HasScheduledPayments,
                                hasRecurringPayments: card.HasRecurringPayments,
                                cardBrand: card.CardBrand,
                              })
                            );

                            router.push({
                              pathname:
                                props?.fields?.data?.item?.EditCardLink?.jsonValue?.value.href,
                            });
                          }}
                        >
                          {props?.fields?.data?.item?.ExpireUpdateCardText?.value}
                        </UnstyledButton>
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </Wrapper>
          );
        })}
      </div>

      <h3 className="flex font-primaryBold text-textUndenary text-base">
        {props?.fields?.data?.item?.AddPaymentMethodText?.value}
      </h3>

      <div className="flex flex-col font-primaryBlack gap-5 lg:flex-row lg:mt-8 sm:lg:mt-0">
        <Button
          type="button"
          variant="secondary"
          size="small"
          className="w-full lg:w-fit h-[auto]"
          onClick={() => props.openAddCardModal()}
        >
          {props?.fields?.data?.item?.AddCardBtnText.value}
        </Button>

        {userProfileData &&
          userProfileData?.result &&
          !userProfileData?.result?.isCashOnly &&
          !userProfileData?.result?.isFraud && (
            <Button
              type="button"
              variant="secondary"
              size="small"
              className="w-full lg:w-fit h-[auto]"
              onClick={() => props.openAddBankModal()}
            >
              {props?.fields?.data?.item?.AddBankAccountBtnText.value}
            </Button>
          )}
      </div>
    </div>
  );
};

export { ManagePaymentMethods };
const Component = withDatasourceCheck()<ManagePaymentMethodsProps>(ManagePaymentMethods);
export default aiLogger(Component, Component.name);
