# For development purposes, note Next.js supports a .env.local
# file, which is already configured to be git ignored.
# Read more about Next.js support of environment variables here:
# https://nextjs.org/docs/basic-features/environment-variables
 
# The public URL to use for absolute URLs, which are required when
# the Next.js app is run within Sitecore editors.
# This should match the `serverSideRenderingEngineApplicationUrl`
# in your Sitecore configuration (see \sitecore\config\nextjs-starter.config).
# Be sure to update these values accordingly as your public endpoint changes.
# See https://jss.sitecore.com/docs/fundamentals/services/view-engine
# If undefined, http://localhost:3000 is used by default.
# In production non-editing environments it is desirable to use relative URLs, so this may be set to empty string.
PUBLIC_URL=http://localhost:3000
 
# To secure the Sitecore editor endpoint exposed by your Next.js app
# (`/api/editing/render` by default), a secret token is used. This (client-side)
# value must match your server-side value (see \sitecore\config\nextjs-starter.config).
# We recommend an alphanumeric value of at least 16 characters.
JSS_EDITING_SECRET=2RNTspJi7oqRDzMZKBH1Df
 
# ====== Sitecore Preview / Delivery Edge ======
 
# Your Sitecore API key is needed to build the app. Typically, the API key is
# defined in `scjssconfig.json` (as `sitecore.apiKey`). This file may not exist
# when building locally (if you've never run `jss setup`), or when building in a
# higher environment (since `scjssconfig.json` is ignored from source control).
# In this case, use this environment variable to provide the value at build time.
SITECORE_API_KEY=
#{B93C9B10-424F-488A-8F6F-9D8049807745}
#UkxYdTlCRjF5SDdkS052NVRvVjJhVmdUd2c5QkVLQkFCVXQyRklDS0l4dz18dmlzdHJhY29ycG84Y2YwLXZpc3RyYXJldGFpZmQ2Mi1kaWdpdGFscHJlcGVmZWMtN2ZlNA==
 
# Your Sitecore API hostname is needed to build the app. Typically, the API host is
# defined in `scjssconfig.json` (as `sitecore.layoutServiceHost`). This file may
# not exist when building locally (if you've never run `jss setup`), or when building
# in a higher environment (since `scjssconfig.json` is ignored from source control).
# In this case, use this environment variable to provide the value at build time.
SITECORE_API_HOST=
 
# Your GraphQL Edge endpoint. This is required for Sitecore Experience Edge.
# For Sitecore XM, this is typically optional. By default, the endpoint is calculated using
# the resolved Sitecore API hostname + the `graphQLEndpointPath` defined in your `package.json`.
GRAPH_QL_ENDPOINT=https://edge.sitecorecloud.io/api/graphql/v1

# GRAPH_QL_ENDPOINT=https://xmc-vistracorpo8cf0-vistraretaifd62-digitalprepefec.sitecorecloud.io/sitecore/api/graph/edge/ide

#https://edge.sitecorecloud.io/api/graphql/v1
#https://xmc-vistracorpo8cf0-vistraretaifd62-digitalprepefec.sitecorecloud.io/sitecore/api/graph/edge
 
# ==============================================
 
# Your Sitecore site name.
# Uses your `package.json` config `appName` if empty.
# When using the Next.js Multisite add-on, the value of the variable represents the default/configured site.
SITECORE_SITE_NAME=veteranmyaccount
 
# Your default app language.
DEFAULT_LANGUAGE=
 
# How many times should GraphQL Layout, Dictionary and ErrorPages services retry a fetch when endpoint rate limit is reached
# You can disable it for all the services by configuring it to 0.
GRAPH_QL_SERVICE_RETRIES=3
 
# The way in which layout and dictionary data is fetched from Sitecore
FETCH_WITH=GraphQL
 
# Indicates whether SSG `getStaticPaths` pre-render any pages
# Set the environment variable DISABLE_SSG_FETCH=true
# to enable full ISR (Incremental Static Regeneration) flow
DISABLE_SSG_FETCH=
 
# Sitecore JSS npm packages utilize the debug module for debug logging.
# https://www.npmjs.com/package/debug
# Set the DEBUG environment variable to 'sitecore-jss:*' to see all logs:
#DEBUG=sitecore-jss:*
# Or be selective and show for example only layout service logs:
#DEBUG=sitecore-jss:layout
# Or everything BUT layout service logs:
#DEBUG=sitecore-jss:*,-sitecore-jss:layout
 
 
# ========== Sitecore Edge Platform ===========
 
# Your unified Sitecore Edge Context Id.
# This will be used over any Sitecore Preview / Delivery Edge variables (above).
SITECORE_EDGE_CONTEXT_ID=4JZyM0x3cWIx29oTF4GXsy

# SITECORE_EDGE_CONTEXT_ID=PnLGlJkEYYWqeNmFwJ3xh

#Master - PnLGlJkEYYWqeNmFwJ3xh
#4JZyM0x3cWIx29oTF4GXsy
 
# ==============================================
 
# An optional Sitecore Personalize scope identifier.
# This can be used to isolate personalization data when multiple XM Cloud Environments share a Personalize tenant.
# This should match the PAGES_PERSONALIZE_SCOPE environment variable for your connected XM Cloud Environment.
NEXT_PUBLIC_PERSONALIZE_SCOPE=
 
# Timeout (ms) for Sitecore CDP requests to respond within. Default is 400.
PERSONALIZE_MIDDLEWARE_CDP_TIMEOUT=
 
# Timeout (ms) for Sitecore Experience Edge requests to respond within. Default is 400.
PERSONALIZE_MIDDLEWARE_EDGE_TIMEOUT=
 
#brand specific oconfiguration
 
NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING='InstrumentationKey=182a1956-82c3-4456-b26b-167a83e1066a;IngestionEndpoint=https://southcentralus-0.in.applicationinsights.azure.com/;LiveEndpoint=https://southcentralus.livediagnostics.monitor.azure.com/'
NEXT_TELEMETRY_DISABLED=1
 
AWS_EKS_URL=https://retailservices.pp.txu.com
 
LP_URL=https://int.landpower.net/digital
 
DT_API_URL=https://dt.pp.txu.com/api/sitecore
 
NEXT_PUBLIC_DT_HOST=https://dt.pp.txu.com
 
AUTHENTICATION_CLIENT_ID=ambt-shopping-client
 
AUTHENTICATION_CLIENT_SECRET=secret
AUTHENTICATION_GRANT_TYPE=client_credentials
 
MYACCOUNT_LOGIN_CLIENT_ID=ambt-dt-client
#MYACCOUNT_LOGIN_CLIENT_ID=dt-tee-client
 
MYACCOUNT_LOGIN_CLIENT_SECRET=secret
#MYACCOUNT_LOGIN_CLIENT_SECRET=qjQdQ2E28mfFx0392pYZ
 
ATA_CLOUDSEARCH_URL=https://ie11ug2t41.execute-api.us-east-1.amazonaws.com
ATA_CLOUDSEARCH_API_KEY=LPnm4Gd2M26q1GaV4tR3u6DYLfeIfZ8K3c2iIsOm
#EXPRESS PAY Identity Token Details
EXPRESSPAY_AUTHENTICATION_CLIENT_ID="expresspay-ambt-client"
EXPRESSPAY_AUTHENTICATION_CLIENT_SECRET="secret"
V2_LOGIN_GRANT_TYPE = "client_credentials"
 
#Captcha Logic
CAPTCHA_YOUR_SECRET_KEY="6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe"
 
NEXT_PUBLIC_PAYMETRICDIECOMMURL=https://cert-xiecomm.worldpay.com/diecomm
NEXT_PUBLIC_PAYMETRIC_ACCESS_TOKEN_URL=/view/iframe/7b9337fb-114d-4f63-8c4d-392298a5f526/{{AccessToken}}/true
PaymetricSharedKey="wH{9M7c?2k}Ft_S4A3\$idP8=5-DpsL6!"
PAYMETRIC_MERCHANT_GUID=7b9337fb-114d-4f63-8c4d-392298a5f526
FONTAWESOME_NPM_AUTH_TOKEN=4818E994-3F46-4D93-B70D-8622858F36E4
 
 
IRON_SESSION_SECRET='RrCES7vUk0kNu0u/UzXbX89cpYGUGRdtSKRAPKlPBME='
 
NEXT_PUBLIC_GOOGLE_APIKEY='AIzaSyDhJ8v4Dl90dnWm2VeilRqlI7DfNKviXto'
 
NEXT_PUBLIC_SITE_NAME='amb'
 
NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN='.ambitenergy.com'
#NEXT_PUBLIC_COOKIES_OPTIONS_DOMAIN='.trieagleenergy.com'
 
NEXT_PUBLIC_SharedCCLoginKey = 'C9-A7-59-93-3E-5A-E8-2A-63-B8-B0-6A-76-EC-FE-3B-4E-DD-D5-6C-19-C1-F9-82'
 
NEXT_PUBLIC_SharedCCLoginIV = '09-3C-61-EA-83-74-92-60'
 
NEXT_PUBLIC_BRAND='AMBT'
NEXT_PUBLIC_REF_ID='Secret'
 
BrandValue='AMBT'
 
LP_BASE_URL = 'https://int.landpower.net'
LP_TOKEN = 'NDBiNzZhYmJhMDAyNGU3ZDkzOTI4YTZkYzRiYTQzZDY='
 
SFMC_AUTH_URL = 'https://mcq78yh75lj1bvcx5ggtxb5ytp-m.auth.marketingcloudapis.com'
SFMC_REST_URL = 'https://mcq78yh75lj1bvcx5ggtxb5ytp-m.rest.marketingcloudapis.com'
SFMC_CLIENT_ID = 'xk0v4j7z35my42edpoe7qhvy'
SFMC_CLIENT_SECRET = 'bDZltKbWQqtM5vOu0okcQoAJ'
SFMC_DEFINITION_KEY = 'REFERRAL_NP'
 
NODE_TLS_REJECT_UNAUTHORIZED=0