import { faPenToSquare, faCalendar } from '@fortawesome/pro-regular-svg-icons';
import { faPen as faPenSolid } from '@fortawesome/pro-solid-svg-icons';
import { faChevronLeft, faChevronRight } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { TextInput } from '@mantine/core';
import { DateInput } from '@mantine/dates';
import { UseFormReturnType } from '@mantine/form';
import { Field, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { PaymentFormType } from '../PaymentContainer/PaymentContainer';
import { useEffect, useRef, useState } from 'react';
import { useMediaQuery } from '@mantine/hooks';
import 'dayjs/locale/es'; // Import Spanish locale

type PaymentDetailsProps = ComponentProps & {
  fields: {
    PaymentAmountLabel: Field<string>;
    PaymentDateLabel: Field<string>;
    DateFormat: Field<string>;
  };
  minPaymentAmount: number;
  minPaymentWarning: string;
  form: UseFormReturnType<PaymentFormType>;
};

const PaymentDetails = (props: PaymentDetailsProps): JSX.Element => {
  const numberInputRef = useRef<HTMLInputElement>(null);
  const dateInputRef = useRef<HTMLInputElement>(null);
  const [formattedPaymentAmount, setFormattedPaymentAmount] = useState('');
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const penIcon = faPenSolid;
  const calendarIcon = faCalendar;

  // Update formattedPaymentAmount when paymentAmount changes
  useEffect(() => {
    if (props.form.values.paymentAmount !== undefined) {
      setFormattedPaymentAmount('$ ' + props.form.values.paymentAmount.toFixed(2));
    }
  }, [props.form.values.paymentAmount]); // Run effect when paymentAmount changes

  const isSpanish = window.location.pathname.startsWith('/es');

  const handlePaymentAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
    const numericValue = value && value > '0' ? parseFloat(value) / 100 : 0.0; // Convert to cents
    const formattedAmount =
      value !== ''
        ? `$ ${new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
          })
            .format(numericValue)
            .replace('$', '')}`
        : '$ 0.00'; // Add $ symbol and remove any extra $
    setFormattedPaymentAmount(formattedAmount);
    props.form.setFieldValue('paymentAmount', numericValue);
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
    props.form.validateField(field);
    if (field === 'paymentAmount') {
      validatePaymentAmount(props.form.values);
    }
  };

  const validatePaymentAmount = (values: typeof props.form.values) => {
 if (!values.paymentAmount || values.paymentAmount <= props.minPaymentAmount) {
      const minPaymentMessage = props?.minPaymentWarning || '';
      props.form.setFieldError('paymentAmount', minPaymentMessage);
      return false;
    }
    props.form.clearFieldError('paymentAmount');
    return true;
  };
  const isDesktop = useMediaQuery('(min-width: 768px)'); // Adjust breakpoint as needed

  return (
    <div className="mp_payment_fields w-full flex md:flex-row flex-row md:px-0 md:w-[590px] gap-4 md:gap-8 tee:gap-4 tee:md:gap-8 items-center sm:items-start text-left tee:text-center tee:sm:text-left">
      <TextInput
        label={props.fields.PaymentAmountLabel.value}
        ref={numberInputRef}
        styles={() => {
          return {
            root: {
              maxWidth: '160px',
              width: 'auto',
            },
            input: {
              fontSize: isDesktop ? '24px' : '20px',
            },
            label: {
              fontSize: '18px',
              color: '#414042',
              fontFamily: 'OpenSans-Bold',
            },
          };
        }}
        value={formattedPaymentAmount}
        onChange={handlePaymentAmountChange}
        onBlur={() => handleBlur('paymentAmount')}
        error={touched.paymentAmount && props.form.errors.paymentAmount}
        rightSection={
          <FontAwesomeIcon
            className="cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1"
            icon={penIcon}
            onClick={() => {
              numberInputRef.current && numberInputRef.current.focus();
            }}
          />
        }
      />
      <DateInput
        locale={isSpanish ? 'es' : 'en'}
        label={props.fields.PaymentDateLabel.value}
        ref={dateInputRef}
        styles={(theme) => {
          return {
            root: {
              maxWidth: '160px',
              width: 'auto',
            },
            label: { fontSize: '14px', color: '#414042', fontFamily: 'OpenSans-Bold' },
            levelsGroup: {
              border: '1px solid #004861',
            },
            calendarHeaderLevel: {
              fontFamily: theme.other.fontFamily?.primaryBold[0],
              color: theme.other.colors.textUndenary[0],
              fontSize: '18px',
            },
            weekday: {
              fontFamily: theme.other.fontFamily?.primaryBold[0],
              color: theme.other.colors.textUndenary[0],
            },
            day: {
              '&[data-selected]': {
                background: '#fff',
                borderBottom: '2px solid #F26D0C',
                color: 'black',
                borderRadius: '0',
                '&:hover': {
                  borderBottom: '2px solid #F26D0C',
                  background: '#fff',
                  color: theme.other.colors.textUndenary[0],
                  borderRadius: '0',
                },
              },
              '&[data-due-date]': {
                backgroundColor: '#F26D0C !important', // Orange background for due date
                color: '#fff', // White text for contrast
                fontWeight: 600,
                borderRadius: '0',
              },
              color: theme.other.colors.textUndenary[0],
              fontFamily: theme.other.fontFamily?.primaryBold[0],
              '&:disabled': {
                color: '#87858E',
                fontFamily: theme.other.fontFamily.primaryRegular[0],
                border: 'none',
              },
              '&:hover': {
                borderBottom: '2px solid #F26D0C',
                color: theme.other.colors.textUndenary[0],
                borderRadius: '0',
                '&:disabled': {
                  color: '#87858E',
                  fontFamily: theme.other.fontFamily.primaryRegular[0],
                },
              },
            },
          };
        }}
        rightSection={
          <FontAwesomeIcon
            icon={calendarIcon}
            className="cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1"
            onClick={() => dateInputRef.current && dateInputRef.current.focus()}
          />
        }
        previousIcon={
          <FontAwesomeIcon
            icon={faChevronLeft}
            className="text-textPrimary hover:text-textSecondary text-plus1"
          />
        }
        nextIcon={
          <FontAwesomeIcon
            icon={faChevronRight}
            className="text-textPrimary hover:text-textSecondary text-plus1"
          />
        }
        minDate={dayjs().toDate()}
        // fix
        maxDate={dayjs().add(1, 'day').toDate()}
        weekendDays={[]}
        {...props.form.getInputProps('paymentDate')}
        valueFormat={props.fields?.DateFormat?.value}
        getDayProps={(date) => {
          const sameDate = dayjs(date).isSame(
            props.form.values.dueDate?.replace(/T00:00:00Z/g, ''),
            'day'
          );
          if (sameDate) {
            return {
              sx: (theme) => ({
                backgroundColor: `${theme?.other?.colors?.bgPrimary?.[0]} !important`,
                color: 'white',
                transform: `translate(0px, -4px)`,
              }),
            };
          }

          return {};
        }}
        renderDay={(date) => {
          const sameDate = dayjs(date).isSame(
            props.form.values.dueDate?.replace(/T00:00:00Z/g, ''),
            'day'
          );
          const day = date.getDate();

          if (sameDate)
            return (
              <div className="flex flex-col items-center">
                <div className="font-primaryRegular text-minus4 translate-y-[2px]">DUE</div>
                <div className="translate-y-[-2px]">{day}</div>
              </div>
            );
          else {
            return day;
          }
        }}
      />
    </div>
  );
};

export { PaymentDetails };
const Component = withDatasourceCheck()<PaymentDetailsProps>(PaymentDetails);
export default aiLogger(Component, Component.name);
