export const CUSTOMER_TYPE = 'residential';
export const COOKIES_OPTIONS_DOMAIN = '.txu.com';
export const COOKIES_LIST = [
  '.JWTAUTHTOKEN',
  'AuthToken',
  'customer_classification',
  'customer_name',
  'PortalType',
  'PlanRenewalDate',
  'isrenewal',
  'ca',
  'esiid',
  'rprom',
  'isImpersonatedUser',
];
export const DAYS_TO_EXPIRE = 45;
export const API_SUCCESS_MSG = 'Success';
export const API_FAILURE_MSG = 'Failure';
export const API_ENROLL_SUBMIT_MSG = 'EnrollmentAlreadySubmitted';
export const PortalType = {
  '0': 'Unknown',
  '1': 'CSPMMF',
  '2': 'Installer',
  '3': 'MarketingPartner',
  '4': 'Residential',
  '5': 'SMBLCI',
  '6': 'SocialAgency',
  '7': 'Prepaid',
};
export const PARAM_AUTH_GUID = 'agv';
export const PARAM_AUTH_GUID_URL = 'url';
export const PARAM_AUTH_ACTION = 'action';
export const PARAM_AUTH_USERNAME = 'username';
