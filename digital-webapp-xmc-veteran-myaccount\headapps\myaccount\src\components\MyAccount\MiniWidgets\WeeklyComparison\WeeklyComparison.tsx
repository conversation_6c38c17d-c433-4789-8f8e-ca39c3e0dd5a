import {
  Text,
  Field,
  withData<PERSON>ur<PERSON><PERSON><PERSON><PERSON>,
  Link,
  LinkField,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { ComponentProps } from 'lib/component-props';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import UsageBar from 'components/common/Charts/UsageBar/UsageBar';
import Button from 'components/Elements/Button/Button';
import { UsageOverviewResponse } from 'src/services/UsageOverviewAPI/types';
import { useAppSelector } from 'src/stores/store';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios-1.4';
import { useEffect, useState } from 'react';
import type { UsageBarData } from 'src/components/common/Charts/UsageBar/UsageBar';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDoubleRight } from '@fortawesome/pro-regular-svg-icons';
import Loader from 'components/common/Loader/Loader';
import { useLoader } from 'src/hooks/modalhooks';
import { logToAppInsights } from 'lib/app-insights-log-error';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

type WeeklyComparisonProps = ComponentProps & {
  fields: {
    data: {
      item: {
        Title: Field<string>;
        CostButtonText: Field<string>;
        UsageButtonText: Field<string>;
        ErrorText: Field<string>;
        PaddingTop: Field<number>;
        PaddingBottom: Field<number>;
        PaddingLeft: Field<number>;
        PaddingRight: Field<number>;
        TopLeft: Field<number>;
        TopRight: Field<number>;
        Chartwidth: Field<number>;
        Barwidth: Field<number>;
        Fillcolor: Field<string>;
        DomainPadding: Field<number>;
        ViewDetailedUsageLabel: Field<string>;
        ViewDetailedUsageLink?: { jsonValue: LinkField };
        DefaultGraphState: Field<string>;
        HideforBusinessUser: Field<boolean>;
        AccountClosedText: Field<string>;
        ErrorTitleText: Field<string>;
      };
    };
  };
};

const WeeklyComparison = (props: WeeklyComparisonProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;

  if (isPageEditing) return <PageBuilder componentName="WeeklyComparison" />;

  let selectedEsiid = undefined;
  let isBusinessUser = undefined;
  let slectedESIIDStatus = undefined;
  if (!isPageEditing) {
    selectedEsiid = useAppSelector((state) => state.authuser?.accountSelection?.esiid?.value);
    isBusinessUser = useAppSelector((state) => state.authuser?.isBusinessUser);
    slectedESIIDStatus = useAppSelector((state) => state?.authuser?.accountSelection.esiid?.status);
  }
  const branding = process.env.NEXT_PUBLIC_BRAND || 'site';
  console.log('Weekly comparison props', props.fields);

  const isDisabledForBsUser =
    isBusinessUser && props.fields.data.item.HideforBusinessUser?.value === true;

  const [graphState, setGraphState] = useState<'cost' | 'usage'>('cost');
  const { openModal } = useLoader();

  const { isLoading, data, error } = useQuery({
    queryKey: ['usageoverview', selectedEsiid],
    queryFn: () =>
      axios
        .get<UsageOverviewResponse>('/api/usageoverview', {
          params: {
            esiID: selectedEsiid,
          },
        })
        .then((res) => {
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Received the usageoverview data for the esiid ${selectedEsiid}`
          );
          return res.data;
        })
        .catch((ex) => {
          logToAppInsights(
            `${branding}-login-myaccount summary-clientid`,
            `Failed to receive the usageoverview data for the esiid ${selectedEsiid}. Exception: ${ex}`
          );
        }),
    enabled: !!selectedEsiid && !isDisabledForBsUser,
  });

  useEffect(() => {
    setGraphState(props.fields?.data.item.DefaultGraphState?.value === 'usage' ? 'usage' : 'cost');
  }, []);

  if (isDisabledForBsUser) return <></>;

  if (isLoading)
    return (
      <div className="w-full h-64">
        <Loader />
      </div>
    );
  if (error)
    return (
      <div className="shadow-none sm:p-0 p-6 rounded-xl w-full lg:m-1/4 md:w-full col-start-1 col-end-7">
        <div className="text-center font-primaryBold text-plus1 sm:text-plus2 text-textUndenary mb-5">
          <Text tag="h2" field={props.fields.data?.item?.Title} />
        </div>
        <div className="text-center relative flex items-center justify-center text-textUndenary sm:text-minus1 font-primaryRegular min-h-[unset]">
          {slectedESIIDStatus?.toLocaleLowerCase() === 'inactive' ? (
            <div
              role="alert"
              className="text-center flex items-center w-full justify-center flex-col border-2 border-borderQuattuordenary rounded-[4px] bg-bgQuaternary px-3 py-7"
            >
              <p className="font-primaryRegular text-textUndenary text-minus1 text-center">
                {props.fields?.data?.item?.AccountClosedText?.value}
              </p>
            </div>
          ) : (
            <div
              role="alert"
              className="flex justify-center flex-col items-start min-h-[135px] text-left border-2 border-borderQuattuordenary rounded-[4px] bg-bgOctodenary p-3"
            >
              <h3 className="flex font-primaryBold text-minus1 text-textUndenary mb-2">
                {props.fields?.data?.item?.ErrorTitleText?.value}
              </h3>
              <p className="font-primaryRegular text-textUndenary text-minus1 text-center">
                {props.fields?.data?.item?.ErrorText?.value}
              </p>
            </div>
          )}
        </div>
      </div>
    );

  if (data && data.result.length > 0) {
    console.log('weekly ', data);
    let chartData: UsageBarData;
    if (graphState === 'cost') {
      chartData = {
        chartType: '$',
        barValue1: data.result[0].billWeek4,
        barValue2: data.result[0].billWeek3,
        barValue3: data.result[0].billWeek2,
        barValue4: data.result[0].billWeek1,
        usageWeekfrom1: data.result[0].week4.split('-')[0].trim(),
        usageWeekfrom2: data.result[0].week3.split('-')[0].trim(),
        usageWeekfrom3: data.result[0].week2.split('-')[0].trim(),
        usageWeekfrom4: data.result[0].week1.split('-')[0].trim(),
        usageWeekto1: data.result[0].week4.split('-')[1].trim(),
        usageWeekto2: data.result[0].week3.split('-')[1].trim(),
        usageWeekto3: data.result[0].week2.split('-')[1].trim(),
        usageWeekto4: data.result[0].week1.split('-')[1].trim(),
        billWeek1: data.result[0].billWeek1,
        billWeek2: data.result[0].billWeek2,
        billWeek3: data.result[0].billWeek3,
        billWeek4: data.result[0].billWeek4,
        paddingTop: props.fields.data?.item?.PaddingTop.value,
        paddingBottom: props.fields.data?.item?.PaddingBottom.value,
        paddingLeft: props.fields.data?.item?.PaddingLeft.value,
        paddingRight: props.fields.data?.item?.PaddingRight.value,
        topLeft: props.fields.data?.item?.TopLeft.value,
        topRight: props.fields.data?.item?.TopRight.value,
        chartwidth: props.fields.data?.item?.Chartwidth.value,
        barwidth: props.fields.data?.item?.Barwidth.value,
        fillcolor: props.fields.data?.item?.Fillcolor.value,
        domainPadding: props.fields.data?.item?.DomainPadding.value,
      };
    } else {
      chartData = {
        chartType: 'kWh',
        barValue1: data?.result[0]?.usageWeek4,
        barValue2: data?.result[0]?.usageWeek3,
        barValue3: data?.result[0]?.usageWeek2,
        barValue4: data?.result[0]?.usageWeek1,
        usageWeekfrom1: data?.result[0]?.week4?.split('-')[0].trim(),
        usageWeekfrom2: data?.result[0]?.week3?.split('-')[0].trim(),
        usageWeekfrom3: data?.result[0]?.week2?.split('-')[0].trim(),
        usageWeekfrom4: data?.result[0]?.week1?.split('-')[0].trim(),
        billWeek1: data?.result[0]?.billWeek1,
        billWeek2: data?.result[0]?.billWeek2,
        billWeek3: data?.result[0]?.billWeek3,
        billWeek4: data?.result[0]?.billWeek4,
        usageWeekto1: data?.result[0]?.week4?.split('-')[1].trim(),
        usageWeekto2: data?.result[0]?.week3?.split('-')[1].trim(),
        usageWeekto3: data?.result[0]?.week2?.split('-')[1].trim(),
        usageWeekto4: data?.result[0]?.week1?.split('-')[1].trim(),
        paddingTop: props?.fields?.data?.item?.PaddingTop.value,
        paddingBottom: props?.fields?.data?.item?.PaddingBottom.value,
        paddingLeft: props?.fields?.data?.item?.PaddingLeft.value,
        paddingRight: props?.fields?.data?.item?.PaddingRight.value,
        topLeft: props?.fields?.data?.item?.TopLeft.value,
        topRight: props?.fields?.data?.item?.TopRight.value,
        chartwidth: props?.fields?.data?.item?.Chartwidth.value,
        barwidth: props?.fields?.data?.item?.Barwidth.value,
        fillcolor: props?.fields?.data?.item?.Fillcolor.value,
        domainPadding: props?.fields?.data?.item?.DomainPadding.value,
      };
    }

    return (
      <div className="w-full sm:max-w-[380px] col-start-1 col-end-7">
        <div className="sm:rounded-xl my-6 mx-0 sm:m-0 shadow-none px-5 py-0">
          <div className="text-base sm:text-plus2 text-textPrimary font-primaryBlack text-start">
            <Text
              tag="h3"
              className="font-primaryBold text-textUndenary text-plus1 sm:text-plus2 text-center"
              field={props.fields?.data?.item?.Title}
            />
          </div>

          {slectedESIIDStatus?.toLocaleLowerCase() === 'inactive' ? (
            <div
              role="alert"
              className="text-center mt-5 flex items-center w-full justify-center flex-col border-2 border-borderQuattuordenary rounded-[4px] bg-bgQuaternary px-3 py-7"
            >
              <p className="font-primaryRegular text-textUndenary text-minus1 text-center">
                {props.fields?.data?.item?.AccountClosedText?.value}
              </p>
            </div>
          ) : (
            <div>
              <UsageBar data={chartData}></UsageBar>
              {props.fields.data?.item?.CostButtonText.value.length > 0 &&
                props.fields.data?.item?.UsageButtonText.value.length > 0 && (
                  <div className="flex gap-2 justify-center my-4">
                    {props.fields.data?.item?.CostButtonText.value.length > 0 && (
                      <Button
                        className={`${
                          graphState === 'cost'
                            ? 'w-full h-[32px] text-minus2 font-primaryBold p-2 text-white border-borderPrimary bg-bgPrimary hover:border-borderSecondary hover:bg-bgSecondary cursor-pointer'
                            : 'w-full h-[32px] text-minus2 font-primaryBold p-2 text-textQuattuordenary border-borderQuattuordenary hover:border-borderSecondary hover:text-hoverSecondary cursor-pointer'
                        }`}
                        type="button"
                        variant={`${graphState === 'cost' ? 'primary' : 'secondary'}`}
                        onClick={() => setGraphState('cost')}
                      >
                        <Text field={props.fields.data?.item?.CostButtonText} />
                      </Button>
                    )}
                    {props.fields.data?.item?.UsageButtonText.value.length > 0 && (
                      <Button
                        className={`${
                          graphState === 'cost'
                            ? 'w-full h-[32px] text-minus2 font-primaryBold p-2 text-white border-borderPrimary bg-bgPrimary hover:border-borderSecondary hover:bg-bgSecondary cursor-pointer'
                            : 'w-full h-[32px] text-minus2 font-primaryBold p-2 text-textQuattuordenary border-borderQuattuordenary hover:border-borderSecondary hover:text-hoverSecondary cursor-pointer'
                        }`}
                        type="button"
                        variant={`${graphState === 'cost' ? 'secondary' : 'primary'}`}
                        onClick={() => setGraphState('usage')}
                      >
                        <Text field={props.fields.data?.item?.UsageButtonText} />
                      </Button>
                    )}
                  </div>
                )}
              {props.fields.data?.item?.ViewDetailedUsageLink?.jsonValue?.value?.href !== '' && (
                <div className="text-minus1 mb-8 inline-link mt-8 text-center">
                  <Link
                    field={{
                      value: {
                        href: props.fields.data?.item?.ViewDetailedUsageLink?.jsonValue?.value
                          ?.href,
                      },
                    }}
                    onClick={() => openModal()}
                  >
                    {props.fields.data?.item?.ViewDetailedUsageLabel.value}
                    <FontAwesomeIcon
                      className="text-minus3 relative top-[1px] hidden pl-4"
                      icon={faChevronDoubleRight}
                    />
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  } else {
    return (
      <div className="shadow-none p-5 sm:p-0 rounded-xl w-full lg:m-1/4 md:w-full col-start-1 col-end-7">
        <div className="text-center font-primaryBold text-plus1 sm:text-plus2 text-textUndenary mb-5">
          <Text tag="h2" field={props.fields.data?.item?.Title} />
        </div>
        <div className="text-center border-borderQuattuordenary rounded-[4px] bg-bgQuaternary relative flex items-center justify-center text-textUndenary sm:text-minus1 font-primaryRegular min-h-[unset]">
          {slectedESIIDStatus?.toLocaleLowerCase() === 'inactive' ? (
            <>
              <div
                role="alert"
                className="text-center flex items-center w-full justify-center flex-col border-2 border-borderQuattuordenary rounded-[4px] bg-bgQuaternary px-3 py-7"
              >
                <p className="font-primaryRegular text-textUndenary text-minus1 text-center">
                  {props.fields?.data?.item?.AccountClosedText?.value}
                </p>
              </div>
            </>
          ) : (
            <div
              role="alert"
              className="flex justify-center flex-col items-start min-h-[135px] text-left border-2 border-borderQuattuordenary rounded-[4px] bg-bgOctodenary p-3"
            >
              <h3 className="flex font-primaryBold text-minus1 text-textUndenary mb-2">
                {props.fields?.data?.item?.ErrorTitleText?.value}
              </h3>
              <p className="font-primaryRegular text-textUndenary text-minus1 text-center">
                {props.fields?.data?.item?.ErrorText?.value}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }
};
export { WeeklyComparison };
const Component = withDatasourceCheck()<WeeklyComparisonProps>(WeeklyComparison);
export default aiLogger(Component, Component.name);
