import {
  faBell,
  faCheck,
  faCircleInfo,
  //faExclamationTriangle,
  faFileMagnifyingGlass,
  faTriangleExclamation,
} from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Loader as MantineLoader, UnstyledButton as MantineButton } from '@mantine/core';
import { faFileLines } from '@fortawesome/pro-solid-svg-icons';
import {
  Field,
  Link,
  LinkField,
  RichText,
  RichTextField,
  Text,
  withDatasourceCheck,
  useSitecoreContext,
} from '@sitecore-jss/sitecore-jss-nextjs';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios-1.4';
import Loader from 'components/common/Loader/Loader';
import Button from 'components/Elements/Button/Button';
import Tooltip from 'components/Elements/Tooltip/Tooltip';
import dayjs from 'dayjs';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import { BillPayCombinedResponse } from 'src/services/BillPayCombinedAPI/types';
import { CustomerDataResponse } from 'src/services/MyAccountAPI/types';
import { UsageOverviewResponse } from 'src/services/UsageOverviewAPI/types';
import { useAppSelector } from 'src/stores/store';
import PageBuilder from 'components/common/PageBuilder/PageBuilder';

interface AccountBalanceProps extends ComponentProps {
  fields: {
    data: {
      item: {
        AccountBalance: Field<string>;
        DueDate: Field<string>;
        ViewBill: Field<string>;
        LastPayment: Field<string>;
        PaymentDate: Field<string>;
        MakeAPayment: Field<string>;
        AutopayScheduledText: Field<string>;
        SignUpForAutoPay: Field<string>;
        NeedMoreTimeToPay: Field<string>;
        OneTimePaymentButtonText: Field<string>;
        OneTimePaymentLink?: { jsonValue: LinkField };
        AutopaySettingsButtonText: Field<string>;
        MakeAPaymentLink?: { jsonValue: LinkField };
        SignUpForAutoPayLink?: { jsonValue: LinkField };
        NeedMoreTimeToPayLink?: { jsonValue: LinkField };
        AccountZeroBalanceDescrition: Field<string>;
        AccountBalanceAllSetText: Field<string>;
        AccountBalanceDueText: Field<string>;
        PastDueMessage: Field<string>;
        PendingDisconnectMessage: Field<string>;
        DisconnectMessage: Field<string>;
        PendingDisconnectDateText: Field<string>;
        ErrorText: Field<string>;
        NoDataErrorMessage: Field<string>;
        Usage: Field<string>;
        Unit: Field<string>;
        DateFormat: Field<string>;
        AccountBalanceDetail: RichTextField;
        PendingDisconnectMessageTooltip: Field<string>;
        PaymybillLink?: { jsonValue: LinkField };
        NextPayment: Field<string>;
        NextPaymentDate: Field<string>;
      };
    };
  };
  params: {
    AccountBalanceVariant?: string;
  };
}

const AccountBalance = ({ params, ...props }: AccountBalanceProps): JSX.Element => {
  const context = useSitecoreContext();
  const isPageEditing = context.sitecoreContext.pageEditing;
  if (isPageEditing) return <PageBuilder componentName="AccountBalance" />;
  let selectedAccount = undefined;
  let selectedAccountBPNumber = undefined;
  let selectedAccountEsiid = undefined;
  let deferralData = undefined;
  if (!isPageEditing) {
    selectedAccount = useAppSelector(
      (state) => state?.authuser?.accountSelection?.contractAccount?.value
    );
    selectedAccountBPNumber = useAppSelector((state) => state?.authuser?.bpNumber);
    selectedAccountEsiid = useAppSelector(
      (state) => state?.authuser?.accountSelection?.esiid?.value
    );
    deferralData = useAppSelector((state) => state?.payment?.deferralData);
  }

  const variant: string = params.AccountBalanceVariant ? params.AccountBalanceVariant : 'myaccount';
  const router = useRouter();
  const { openModal, closeAllModal } = useLoader();

  const { isLoading, data, error, isFetching, isRefetching } = useQuery({
    queryKey: ['accountbalance', selectedAccount],
    queryFn: () =>
      axios
        .get<BillPayCombinedResponse>('/api/billpaycombined', {
          params: {
            accountNumber: selectedAccount,
          },
        })
        .then((res) => res.data),
    enabled: !!selectedAccount,
  });

  console.log('data=',data);

  
  const { data: currentuserdata } = useQuery({
    queryKey: ['currentuserdata', selectedAccount, selectedAccountBPNumber, selectedAccountEsiid],
    queryFn: () =>
      axios
        .get<CustomerDataResponse>('/api/myaccount/customer', {
          params: {
            partnerNumber: selectedAccountBPNumber,
            accountNumber: selectedAccount,
            esiid: selectedAccountEsiid,
          },
        })
        .then((res) => res.data),
    enabled:
      (!!selectedAccount &&
        !!selectedAccountBPNumber &&
        selectedAccountEsiid != '' &&
        variant === 'myaccount') ||
      (!!selectedAccount &&
        !!selectedAccountBPNumber &&
        selectedAccountEsiid != '' &&
        variant === 'teemyaccount'),
  });

  useQuery({
    queryKey: ['usageoverview', selectedAccountEsiid],
    queryFn: () =>
      axios
        .get<UsageOverviewResponse>('/api/usageoverview', {
          params: {
            esiID: selectedAccountEsiid,
          },
        })
        .then((res) => res.data),
    enabled:
      (!!selectedAccount &&
        !!selectedAccountBPNumber &&
        selectedAccountEsiid != '' &&
        variant === 'payment') ||
      (!!selectedAccount &&
        !!selectedAccountBPNumber &&
        selectedAccountEsiid != '' &&
        variant === 'billing'),
  });

  function makePaymentRedirection(_isOneTimePayment: boolean) {
    openModal();
    router.push(props.fields.data.item.PaymybillLink?.jsonValue.value?.href as string);
  }
  function redirectPdfUrl(archiveId: string, documentId: string) {
    window.open('/PdfViewer?arcid=' + archiveId + '&docid=' + documentId, '_blank');
  }

  const viewPDF = async (archiveId: string, documentId: string) => {
    openModal();
    try {
      redirectPdfUrl(
        archiveId == '' ? 'undefined' : archiveId,
        documentId == '' ? 'undefined' : documentId
      ); //#46598 if empty string from response adding undefined string
      closeAllModal();
    } catch (error) {
      closeAllModal();
    }
  };

  const nextBillDate = data?.result?.nextBillDueDate.replace(/T00:00:00Z/g, '');

  let dueDate = data?.result?.accountDetails?.invoiceDetails?.billDueDate?.replace(
    /T00:00:00Z/g,
    ''
  );

  console.log('nextBillDate=',nextBillDate,'dueDate=',dueDate);

  if (deferralData && deferralData?.dueDate && deferralData?.dueDate !== '') {
    dueDate = deferralData.dueDate;
  }

  if ((isLoading || isFetching || isRefetching) && variant === 'myaccount') {
    return (
      <>
        <div className="w-full md:w-1/3 lg:m-1/4 sm:p-0 sm:flex-1">
          <div className="shadow-3xl rounded-xl flex justify-center items-center min-h-[200px] p-5">
            <MantineLoader color="blue" />
          </div>
        </div>
        <div className="w-full md:w-1/3 lg:m-1/4 sm:p-0 sm:flex-1">
          <div className="shadow-3xl rounded-xl flex justify-center items-center min-h-[200px] p-5">
            <MantineLoader color="blue" />
          </div>
        </div>
        <div className="w-full md:w-1/3 lg:m-1/4 sm:p-0 sm:flex-1">
          <div className="flex justify-center items-center min-h-[200px] p-5">
            <MantineLoader color="blue" />
          </div>
        </div>
      </>
    );
  } else if (isLoading || isFetching || isRefetching) {
    return (
      <>
        <div className="w-full h-56 sm:h-full">
          <Loader />
        </div>
      </>
    );
  }

  if (error && variant === 'myaccount')
    return (
      <>
        <div className="w-full md:w-1/3 lg:m-1/4 p-8 sm:p-0 ">
          <div className="shadow-3xl p-5 rounded-xl flex justify-center items-center min-h-[200px]">
            <Text field={props.fields.data.item.ErrorText} />
          </div>
        </div>
        <div className="w-full md:w-1/3 lg:m-1/4 p-8 sm:p-0 ">
          <div className="shadow-3xl p-5 rounded-xl flex justify-center items-center min-h-[200px]">
            <Text field={props.fields.data.item.ErrorText} />
          </div>
        </div>
      </>
    );

  if (variant === 'myaccount')
    return (
      <>
        {data && data?.result && data?.result?.accountDetails ? (
          <>
            {/* Account Balance */}

            {Number(data?.result?.amount) <= 0 ? (
              <>
                <div className="w-full md:w-1/3 lg:m-1/4 p-0">
                  <div className="sm:shadow-3xl sm:rounded-xl text-center flex flex-col items-center justify-center h-full gap-3">
                    <span className="text-minus1 sm:text-base text-textQuattuordenary leading-[30px]">
                      <Text field={props.fields.data.item.AccountBalance} />
                    </span>
                    <span
                      tabIndex={0}
                      className="text-plus4 text-textPrimary font-primaryBlack"
                      aria-live="polite"
                      aria-atomic="true"
                      aria-label={'Account Balance $' + data?.result?.amount}
                    >
                      <FontAwesomeIcon
                        className="bg-bgSexdenary rounded-full w-[16px] h-[16px] p-1"
                        icon={faCheck}
                      />{' '}
                      ${data?.result.amount}
                      {data?.result?.disconnectDataResult?.results &&
                      data?.result?.disconnectDataResult?.results?.length > 0 ? (
                        <>{props.fields.data.item.PastDueMessage?.value}</>
                      ) : (
                        ''
                      )}
                    </span>
                    <span className="text-sm">
                      {props.fields?.data.item.AccountZeroBalanceDescrition?.value.replace(
                        '{DueDate}',
                        dayjs(dueDate).format(
                          props?.fields?.data.item.DateFormat?.value?.length > 0
                            ? props?.fields?.data.item.DateFormat?.value
                            : 'MM/DD/YYYY'
                        )
                      )}
                    </span>
                  </div>
                </div>
                {/* Payment Info */}
                <div className="w-full md:w-1/3 lg:m-1/4 p-0">
                  <div className="shadow-3xl p-5 rounded-xl h-full">
                    <div className="flex items-center">
                      {/* hide duedate when showing make a payment */}
                      <div className="text-textPrimary hover:text-textSecondary text-minus1 text-center basis-2/4">
                        <Link
                          field={props.fields.data.item.MakeAPaymentLink?.jsonValue.value || {}}
                          onClick={() => openModal()}
                          className="font-primaryBlack"
                        >
                          <Text field={props.fields.data.item.MakeAPayment} />
                        </Link>
                      </div>
                      <div className="text-textPrimary hover:text-textSecondary text-minus1 text-center basis-2/4">
                        <a
                          href="#"
                          className="font-primaryBlack"
                          onClick={() =>
                            viewPDF(
                              data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                              data?.result?.accountDetails?.invoiceDetails?.documentId
                            )
                          }
                        >
                          <Text field={props.fields.data.item.ViewBill} />{' '}
                          <FontAwesomeIcon className="pl-2" icon={faFileMagnifyingGlass} />
                        </a>
                      </div>
                    </div>
                    <hr className="w-full border-1 my-4"></hr>
                    <div className="flex justify-center items-center">
                      <div className="text-base text-textQuattuordenary text-center basis-2/4">
                        <span className="font-primaryBold">
                          <Text field={props.fields.data.item.LastPayment} />
                        </span>
                        <br />
                        <span
                          tabIndex={0}
                          className="font-primaryRegular"
                          aria-label={
                            'Last Payment ' + data?.result?.lastPayments?.results[0]?.amount
                          }
                        >
                          ${data?.result?.lastPayments?.results[0]?.amount}
                        </span>
                      </div>
                      <div className="text-base text-textQuattuordenary text-center basis-2/4">
                        <span className="font-primaryBold">
                          <Text field={props.fields.data.item.PaymentDate} />
                        </span>
                        <br />
                        <span
                          tabIndex={0}
                          className="font-primaryRegular"
                          aria-label={
                            'Payment Date ' +
                            dayjs(
                              data?.result.lastPayments?.results[0]?.postingDate?.replace(
                                /T00:00:00Z/g,
                                ''
                              )
                            )
                              .format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )
                              .toString()
                          }
                        >
                          {dayjs(
                            data?.result?.lastPayments?.results[0]?.postingDate?.replace(
                              /T00:00:00Z/g,
                              ''
                            )
                          ).format(
                            props?.fields?.data.item.DateFormat?.value?.length > 0
                              ? props?.fields?.data.item.DateFormat?.value
                              : 'MM/DD/YYYY'
                          )}
                        </span>
                      </div>
                    </div>
                    {data?.result?.nextPayment != null && (
                      <>
                        <hr className="w-full border-1 my-4"></hr>
                        <div className="flex justify-center items-center">
                          <div className="text-base text-textQuattuordenary text-center basis-2/4">
                            <span className="font-primaryBold">
                              <Text field={props.fields.data.item.NextPayment} />
                            </span>
                            <br />
                            <span
                              tabIndex={0}
                              className="font-primaryRegular"
                              aria-label={'Last Payment ' + data?.result?.nextPayment?.amount}
                            >
                              ${data?.result?.nextPayment.amount}
                            </span>
                          </div>
                          <div className="text-base text-textQuattuordenary text-center basis-2/4">
                            <span className="font-primaryBold">
                              <Text field={props.fields.data.item.NextPaymentDate} />
                            </span>
                            <br />
                            <span
                              tabIndex={0}
                              className="font-primaryRegular"
                              aria-label={
                                'Payment Date ' +
                                dayjs(
                                  data?.result?.nextPayment.paymentDate?.replace(/T00:00:00Z/g, '')
                                )
                                  .format(
                                    props?.fields?.data.item.DateFormat?.value?.length > 0
                                      ? props?.fields?.data.item.DateFormat?.value
                                      : 'MM/DD/YYYY'
                                  )
                                  .toString()
                              }
                            >
                              {dayjs(
                                data?.result?.nextPayment?.paymentDate.replace(/T00:00:00Z/g, '')
                              ).format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )}
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="w-full max-w-[280px]">
                  <div className="sm:shadow-3xl sm:rounded-xl text-center flex flex-col items-center justify-center h-full gap-3">
                    <span className="text-minus1 sm:text-base text-textQuattuordenary leading-[30px]">
                      <Text field={props.fields.data.item.AccountBalance} />
                    </span>
                    <span
                      tabIndex={0}
                      className="text-plus4 sm:text-plus5 text-textPrimary font-primaryBlack"
                      aria-live="polite"
                      aria-atomic="true"
                      aria-label={'Account Balance $' + data?.result?.amount}
                    >
                      {`$${data?.result?.amount}`}
                      {data?.result?.disconnectDataResult != undefined &&
                      data?.result?.disconnectDataResult?.results?.length > 0 ? (
                        <div className="text-minus1 text-cardinal">
                          <FontAwesomeIcon icon={faBell} />
                          <span className="ml-2">
                            {props.fields.data.item.PastDueMessage?.value}
                          </span>
                        </div>
                      ) : (
                        ''
                      )}
                    </span>
                  </div>
                </div>
                {/* Payment Info */}
                <div className="w-full sm:max-w-[330px]">
                  <div className="shadow-3xl p-5 rounded-xl h-full">
                    <div className="flex items-center">
                      {/* hide duedate when showing make a payment */}
                      <div className="text-minus1 sm:text-base text-textQuattuordenary text-center basis-2/4">
                        <span className="font-primaryBold">
                          <Text field={props.fields.data.item.DueDate} />
                        </span>
                        <br />
                        <span
                          tabIndex={0}
                          className="font-primaryRegular"
                          aria-label={
                            'Due Date ' +
                            dayjs(dueDate)
                              .format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )
                              .toString()
                          }
                        >
                          {dayjs(dueDate)
                            .format(
                              props?.fields?.data.item.DateFormat?.value?.length > 0
                                ? props?.fields?.data.item.DateFormat?.value
                                : 'MM/DD/YYYY'
                            )
                            .toString()}
                        </span>
                      </div>
                      <div className="text-textPrimary text-minus1 text-center basis-2/4 hover:text-textSecondary">
                        <a
                          href="#"
                          className="font-primaryBlack"
                          onClick={() =>
                            viewPDF(
                              data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                              data?.result?.accountDetails?.invoiceDetails?.documentId
                            )
                          }
                        >
                          <Text field={props.fields.data.item.ViewBill} />{' '}
                          <FontAwesomeIcon className="pl-2" icon={faFileMagnifyingGlass} />
                        </a>
                      </div>
                    </div>
                    <hr className="w-full border-1 my-4"></hr>
                    <div className="flex justify-center items-center">
                      <div className="text-minus1 sm:text-base text-textQuattuordenary text-center basis-2/4">
                        <span className="font-primaryBold">
                          <Text field={props.fields.data.item.LastPayment} />
                        </span>
                        <br />
                        <span
                          tabIndex={0}
                          className="font-primaryRegular"
                          aria-label={
                            'Last Payment ' + data?.result?.lastPayments?.results[0]?.amount
                          }
                        >
                          ${data?.result?.lastPayments?.results[0]?.amount}
                        </span>
                      </div>
                      <div className="text-minus1 sm:text-base text-textQuattuordenary text-center basis-2/4">
                        <span className="font-primaryBold">
                          <Text field={props.fields.data.item.PaymentDate} />
                        </span>
                        <br />
                        <span
                          tabIndex={0}
                          className="font-primaryRegular"
                          aria-label={
                            'Payment Date ' +
                            dayjs(
                              data?.result?.lastPayments?.results[0]?.postingDate.replace(
                                /T00:00:00Z/g,
                                ''
                              )
                            )
                              .format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )
                              .toString()
                          }
                        >
                          {dayjs(
                            data?.result?.lastPayments?.results[0]?.postingDate.replace(
                              /T00:00:00Z/g,
                              ''
                            )
                          )
                            .format(
                              props?.fields?.data.item.DateFormat?.value?.length > 0
                                ? props?.fields?.data.item.DateFormat?.value
                                : 'MM/DD/YYYY'
                            )
                            .toString()}
                        </span>
                      </div>
                    </div>
                    {data?.result?.nextPayment != null && (
                      <>
                        <hr className="w-full border-1 my-4"></hr>
                        <div className="flex justify-center items-center">
                          <div className="text-base text-textQuattuordenary text-center basis-2/4">
                            <span className="font-primaryBold">
                              <Text field={props.fields.data.item.NextPayment} />
                            </span>
                            <br />
                            <span
                              tabIndex={0}
                              className="font-primaryRegular"
                              aria-label={'Last Payment ' + data?.result?.nextPayment?.amount}
                            >
                              ${data?.result?.nextPayment.amount}
                            </span>
                          </div>
                          <div className="text-base text-textQuattuordenary text-center basis-2/4">
                            <span className="font-primaryBold">
                              <Text field={props.fields.data.item.NextPaymentDate} />
                            </span>
                            <br />
                            <span
                              tabIndex={0}
                              className="font-primaryRegular"
                              aria-label={
                                'Payment Date ' +
                                dayjs(
                                  data?.result?.nextPayment.paymentDate?.replace(/T00:00:00Z/g, '')
                                )
                                  .format(
                                    props?.fields?.data.item.DateFormat?.value?.length > 0
                                      ? props?.fields?.data.item.DateFormat?.value
                                      : 'MM/DD/YYYY'
                                  )
                                  .toString()
                              }
                            >
                              {dayjs(
                                data?.result?.nextPayment?.paymentDate.replace(/T00:00:00Z/g, '')
                              ).format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )}
                            </span>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
                {/* Buttons */}
                <div className="w-full sm:max-w-[230px]">
                  <div className="flex flex-col gap-4">
                    {(data?.result?.disconnectDataResult != undefined &&
                      data?.result?.disconnectDataResult?.results?.length > 0 &&
                      data?.result?.dunningMessage?.dunningLevel?.toLowerCase() ===
                        'dunninglevel30') ||
                    data?.result?.dunningMessage?.dunningLevel?.toLowerCase() ===
                      'dunninglevel50' ? (
                      <div className="flex flex-col gap-1 text-minus1 text-cardinal text-center">
                        <span className="font-primaryBlack">
                          {props.fields.data.item.PendingDisconnectMessage?.value}
                          <Tooltip
                            content={props.fields.data.item.PendingDisconnectMessageTooltip}
                            className={`selected-tooltip`}
                            arrowclassName="selected-tooltip-icon"
                          >
                            <FontAwesomeIcon className="ml-2" icon={faCircleInfo} />
                          </Tooltip>
                        </span>
                        <span className="font-primaryRegular">
                          {props.fields.data.item.PendingDisconnectDateText?.value}
                          <span className="font-primaryBlack ml-1">
                            {data?.result?.disconnectDataResult?.results?.length > 0
                              ? dayjs(
                                  data?.result?.disconnectDataResult?.results[0]?.disconnectionDate?.replace(
                                    /T00:00:00Z/g,
                                    ''
                                  )
                                )
                                  .format(
                                    props?.fields?.data.item.DateFormat?.value?.length > 0
                                      ? props?.fields?.data.item.DateFormat?.value
                                      : 'MM/DD/YYYY'
                                  )
                                  .toString()
                              : ''}
                          </span>
                        </span>
                      </div>
                    ) : (
                      ''
                    )}
                    {currentuserdata && currentuserdata?.result?.isAutoPayEnrolled === 'Y' ? (
                      <>
                        <span className="text-minus1 font-primaryBold text-textPrimary text-center">
                          {props.fields.data.item.AutopayScheduledText.value.replace(
                            '{Date}',
                            dayjs(dueDate).format('MM/DD').toString()
                          )}
                        </span>

                        <Button
                          className="px-0 w-full mb-4"
                          onClick={() => makePaymentRedirection(true)}
                        >
                          {props.fields.data.item.OneTimePaymentButtonText.value}
                        </Button>
                        <Link
                          field={
                            props.fields?.data.item.SignUpForAutoPayLink?.jsonValue.value || {}
                          }
                          // target="_blank"
                          className="flex items-center justify-center border-2 border-solid border-borderPrimary hover:border-borderPrimary rounded-[100px] h-[52px] text-center text-base text-textPrimary font-primaryBlack hover:text-textSecondary focus:outline-none focus:ring-4 focus:ring-textPrimary focus:ring-offset-2 "
                        >
                          <Text field={props.fields.data.item.AutopaySettingsButtonText} />
                        </Link>
                      </>
                    ) : (
                      <div>
                        <Button
                          className="px-0 w-full mb-4"
                          onClick={() => makePaymentRedirection(false)}
                        >
                          {props.fields.data.item.MakeAPayment.value}
                        </Button>
                        <Link
                          field={
                            props.fields?.data.item.SignUpForAutoPayLink?.jsonValue.value || {}
                          }
                          // target="_blank"
                          className="flex items-center justify-center border-2 border-solid border-borderPrimary hover:border-borderPrimary rounded-[100px] h-[56px] text-center text-base text-textPrimary font-primaryBlack hover:text-textSecondary focus:outline-none focus:ring-4 focus:ring-textPrimary focus:ring-offset-2 "
                        >
                          {props.fields.data.item.SignUpForAutoPay.value}
                        </Link>
                      </div>
                    )}
                    {data && data?.result?.deferEligibile && !data.result.isOnDeferralPath ? (
                      <Link
                        field={props.fields?.data.item.NeedMoreTimeToPayLink?.jsonValue.value || {}}
                        // target="_blank"
                        className="flex items-center justify-center hover:border-borderPrimary rounded-[100px] h-[56px] text-center text-base text-textPrimary font-primaryBlack hover:text-textSecondary focus:outline-none focus:ring-4 focus:ring-textPrimary focus:ring-offset-2 "
                      >
                        {props.fields.data.item.NeedMoreTimeToPay.value}
                      </Link>
                    ) : (
                      <></>
                    )}
                  </div>
                </div>
              </>
            )}
          </>
        ) : (
          <div className="shadow-3xl p-6 rounded-xl w-full md:w-1/3 lg:m-1/4">
            <div className="text-center text-plus2 text-textPrimary font-primaryBlack  ">
              {/* <Text field={props.fields.Title} /> */}
            </div>
            <div className="text-center flex items-center justify-center min-h-[200px]">
              <Text field={props.fields.data.item.NoDataErrorMessage} />
            </div>
          </div>
        )}
      </>
    );

  if (isFetching || isRefetching || (isLoading && variant === 'teemyaccount'))
    return (
      <>
        <div className="sm:w-[280px] w-full p-8 md:p-4 sm:shadow-3xl m-6 sm:m-0">
          <div className="flex justify-center items-center min-h-[200px] p-5">
            <MantineLoader color="blue" />
          </div>
        </div>
      </>
    );

  if (variant === 'teemyaccount')
    return (
      <>
        {data && data?.result && data?.result?.accountDetails ? (
          <>
            {Number(data?.result.amount) <= 0 ? (
              <>
                {' '}
                <div className="justify-center items-center">
                  <div className="text-center sm:w-[280px] px-[15px] sm:px-0">
                    <div className="flex justify-between items-center font-primaryBold">
                      <Text field={props.fields?.data.item.AccountBalance} />
                      <div className=" font-primaryBold underline ">
                        <a
                          href="#"
                          className="font-primaryBold text-textPrimary hover:text-textSecondary flex gap-2 "
                          onClick={() =>
                            viewPDF(
                              data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                              data?.result?.accountDetails?.invoiceDetails?.documentId
                            )
                          }
                        >
                          <Text field={props.fields.data.item.ViewBill} />{' '}
                          <FontAwesomeIcon
                            className={` block text-minus1 relative top-[3px] `}
                            icon={faFileLines}
                          />
                        </a>
                      </div>
                    </div>
                    <div className="text-[44px] leading-[52px] text-textUndenary font-primaryBold text-left">
                      ${data?.result?.amount}
                    </div>
                    <div className=" font-primaryRegular text-base text-left">
                      <br />
                      <div className="block leading-[24px] text-base mt-2 sm:mt-0">
                        {data?.result?.nextBillDueDate !== '0001-01-01T00:00:00' && (
                          <span>
                            {props.fields.data?.item?.AccountBalanceAllSetText.value.replace(
                              '{DueDate}',
                              dayjs(
                                data?.result?.nextBillDueDate.replace(/T00:00:00Z/g, '')
                              ).format(
                                props?.fields?.data?.item?.DateFormat?.value?.length > 0
                                  ? props?.fields?.data?.item?.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )
                            )}
                          </span>
                        )}
                      </div>
                      <div className="hidden">
                        {data?.result?.accountDetails?.invoiceDetails?.billDueDate !==
                          '0001-01-01T00:00:00' && (
                          <span className="text-sm font-primaryBold">
                            {props.fields.data.item.AccountBalanceDueText.value.replace(
                              '{DueDate}',
                              dayjs(dueDate).format(
                                props?.fields?.data.item.DateFormat?.value?.length > 0
                                  ? props?.fields?.data.item.DateFormat?.value
                                  : 'MM/DD/YYYY'
                              )
                            )}
                          </span>
                        )}
                      </div>
                    </div>

                    <br />
                    <div className="flex justify-between items-center">
                      <div className="  hover:text-textPrimary text-minus3 leading-3 font-primaryBold">
                        <Button
                          type="button"
                          className="w-full h-[56px] border-solid border-2"
                          variant="primary"
                          showLoader={true}
                          onClick={() => {
                            router.push({
                              pathname:
                                props.fields.data.item.MakeAPaymentLink?.jsonValue.value.href || '',
                            });
                          }}
                        >
                          <Text field={props.fields.data.item.MakeAPayment} />
                        </Button>
                        <Button
                          type="button"
                          className=" mt-4 w-full h-[56px]"
                          variant="secondary"
                          showLoader={true}
                          onClick={() => {
                            router.push({
                              pathname:
                                props?.fields?.data?.item?.SignUpForAutoPayLink?.jsonValue?.value
                                  ?.href,
                            });
                          }}
                        >
                          <Text
                            field={
                              currentuserdata && currentuserdata?.result?.isAutoPayEnrolled === 'Y'
                                ? props.fields.data.item.AutopaySettingsButtonText
                                : props.fields.data.item.SignUpForAutoPay
                            }
                          />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="text-center sm:w-[280px] px-[15px] sm:px-0">
                  <div className="flex justify-between items-center font-primaryBold">
                    <Text field={props.fields?.data.item.AccountBalance} />
                    <div className=" font-primaryBold underline ">
                      <a
                        href="#"
                        className="font-primaryBold hover:text-textPrimary text-textSecondary flex gap-2 "
                        onClick={() =>
                          viewPDF(
                            data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                            data?.result?.accountDetails?.invoiceDetails?.documentId
                          )
                        }
                      >
                        <Text field={props.fields.data.item.ViewBill} />{' '}
                        <FontAwesomeIcon
                          className={` block text-minus1 relative top-[3px] `}
                          icon={faFileLines}
                        />
                      </a>
                    </div>
                  </div>
                  <div className="text-[44px] leading-[52px] text-textUndenary font-primaryBold text-left">
                    {`$${data?.result?.amount || '0'}`}
                  </div>
                  <div className=" font-primaryRegular text-base text-left">
                    <br />
                    <Text field={props.fields.data.item.DueDate} />{' '}
                    {dayjs(dueDate).format(
                      props?.fields?.data.item.DateFormat?.value?.length > 0
                        ? props?.fields?.data.item.DateFormat?.value
                        : 'MM/DD/YYYY'
                    )}
                  </div>
                  {/* Past Due Section */}
                  {data?.result?.disconnectDataResult != undefined &&
                  data?.result?.disconnectDataResult?.results?.length > 0 ? (
                    <div className="sm:text-base text-minus2 text-textDenary font-primaryRegular mt-3 text-left">
                      <FontAwesomeIcon icon={faTriangleExclamation} />
                      <span className="ml-2">{props.fields.data.item.PastDueMessage?.value}</span>
                    </div>
                  ) : (
                    ''
                  )}
                  {/* Pending Disconnect Section */}
                  {(data?.result?.disconnectDataResult != undefined &&
                    data?.result?.disconnectDataResult?.results?.length > 0 &&
                    data?.result?.dunningMessage?.dunningLevel?.toLowerCase() ===
                      'dunninglevel30') ||
                  data?.result?.dunningMessage?.dunningLevel?.toLowerCase() === 'dunninglevel50' ? (
                    <div className="sm:text-minus1 text-minus2 text-textDenary text-left">
                      <span className="font-primaryRegular">
                        {props.fields.data.item.PendingDisconnectMessage?.value}
                        <span className="font-primaryBlack ml-1">
                          {data?.result?.disconnectDataResult?.results?.length > 0
                            ? dayjs(
                                data?.result?.disconnectDataResult?.results[0]?.disconnectionDate?.replace(
                                  /T00:00:00Z/g,
                                  ''
                                )
                              )
                                .format(
                                  props?.fields?.data.item.DateFormat?.value?.length > 0
                                    ? props?.fields?.data.item.DateFormat?.value
                                    : 'MM/DD/YYYY'
                                )
                                .toString()
                            : ''}
                        </span>
                      </span>
                    </div>
                  ) : (
                    ''
                  )}
                  <div className="my-6">
                    {currentuserdata && currentuserdata?.result?.isAutoPayEnrolled === 'Y' ? (
                      <>
                        <div>
                          <span className="text-minus1 font-primaryBold text-textUndenary text-left">
                            {props.fields.data.item.AutopayScheduledText.value.replace(
                              '{Date}',
                              dayjs(dueDate).format('MM/DD').toString()
                            )}
                          </span>

                          <Button
                            type="button"
                            className="disabled:border-borderPrimary disabled:text-textPrimary disabled:opacity-100 mt-4 w-full h-[56px] border-solid border-2"
                            variant="primary"
                            showLoader={true}
                            onClick={() => {
                              router.push({
                                pathname:
                                  props.fields.data.item.OneTimePaymentLink?.jsonValue.value.href,
                              });
                            }}
                          >
                            {props.fields?.data.item.OneTimePaymentButtonText.value}
                          </Button>
                        </div>
                        <div>
                          <Button
                            type="button"
                            className="disabled:border-borderPrimary disabled:text-textPrimary disabled:opacity-100 mt-4 w-full h-[56px]"
                            variant="secondary"
                            showLoader={true}
                            onClick={() => {
                              router.push({
                                pathname:
                                  props.fields.data.item.SignUpForAutoPayLink?.jsonValue.value.href,
                              });
                            }}
                          >
                            <Text field={props.fields.data.item.AutopaySettingsButtonText} />
                          </Button>
                        </div>
                      </>
                    ) : (
                      <>
                        {' '}
                        <div>
                          <Button
                            type="button"
                            className="disabled:border-borderPrimary disabled:text-textPrimary disabled:opacity-100 mt-4 w-full h-[56px] border-solid border-2"
                            variant="primary"
                            onClick={() => {
                              openModal(),
                                router.push({
                                  pathname:
                                    props.fields.data.item.MakeAPaymentLink?.jsonValue.value?.href,
                                });
                            }}
                          >
                            <Text field={props.fields.data.item.MakeAPayment} />
                          </Button>
                        </div>
                        <div>
                          <Button
                            type="button"
                            className="disabled:border-borderPrimary disabled:text-textPrimary disabled:opacity-100 mt-4 w-full h-[56px]"
                            variant="secondary"
                            onClick={() => {
                              openModal(),
                                router.push({
                                  pathname:
                                    props.fields.data.item.SignUpForAutoPayLink?.jsonValue.value
                                      ?.href,
                                });
                            }}
                          >
                            <Text field={props.fields.data.item.SignUpForAutoPay} />
                          </Button>
                        </div>
                      </>
                    )}
                  </div>
                  <div className="flex justify-between items-center">
                    {data && data?.result?.deferEligibile && !data.result.isOnDeferralPath ? (
                      <div className="text-minus3 inline-link font-primaryBold decoration-2">
                        <Link
                          field={
                            props.fields.data.item.NeedMoreTimeToPayLink?.jsonValue.value || {}
                          }
                          className=""
                        >
                          <Text field={props.fields.data.item.NeedMoreTimeToPay} />
                        </Link>
                      </div>
                    ) : (
                      <div className="text-minus3 inline-link font-primaryBold decoration-2"></div>
                    )}
                  </div>
                </div>{' '}
              </>
            )}
          </>
        ) : (
          <div className="shadow-3xl p-6 rounded-xl w-full md:w-1/3 lg:m-1/4">
            <div className="text-center text-plus2 text-textPrimary font-primaryBlack">
              {/* <Text field={props.fields.Title} /> */}
            </div>
            <div className="text-center flex items-center justify-center min-h-[200px]">
              <Text field={props.fields.data.item.NoDataErrorMessage} />
            </div>
          </div>
        )}
      </>
    );

  if (error && variant === 'teemyaccount')
    return (
      <>
        <div className="w-full md:w-1/3 lg:m-1/4 p-8 sm:p-0 ">
          <div className="shadow-3xl p-5 rounded-xl flex justify-center items-center min-h-[200px]">
            <Text field={props.fields.data.item.ErrorText} />
          </div>
        </div>
        <div className="w-full md:w-1/3 lg:m-1/4 p-8 sm:p-0 ">
          <div className="shadow-3xl p-5 rounded-xl flex justify-center items-center min-h-[200px]">
            <Text field={props.fields.data.item.ErrorText} />
          </div>
        </div>
      </>
    );

  if (variant === 'payment' && data && data?.result && data?.result?.accountDetails) {
    // ref
    return (
      <div className="flex flex-col sm:flex-row items-center md:justify-between gap-1">
        <RichText
          className="text-minus1 sm:text-base text-textQuattuordenary font-primaryRegular text-center md:text-left"
          field={{
            value: props.fields.data.item.AccountBalanceDetail.value
              ?.replace('{AccountBalance}', '$' + data?.result?.amount)
              .replace(
                '{DueDate}',
                dayjs(Number(data?.result?.amount) <= 0 ? nextBillDate : dueDate)
                  .format(
                    props?.fields?.data?.item?.DateFormat?.value?.length > 0
                      ? props?.fields?.data?.item?.DateFormat?.value
                      : 'MM/DD/YYYY'
                  )
                  .toString()
              ),
          }}
        />

        <div className="flex flex-col gap-0 sm:gap-1">
          {/* {usageoverViewData && usageoverViewData?.result.length > 0 && (
            <div className="text-minus1 sm:text-base text-textQuattuordenary font-primaryRegular md:text-lg">
              {props.fields.Usage.value}: {usageoverViewData?.result[0]?.usageToDate}{' '}
              {props.fields.Unit.value}
            </div>
          )} */}
          <MantineButton
            className="font-primaryBold text-minus1 mt-2 text-textSecondary hover:text-textPrimary decoration-textPrimary hover:decoration-textPrimary"
            onClick={() =>
              viewPDF(
                data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                data?.result?.accountDetails?.invoiceDetails?.documentId
              )
            }
          >
            {props.fields.data.item.ViewBill.value}
            <FontAwesomeIcon className="pl-2" icon={faFileMagnifyingGlass} />
          </MantineButton>
        </div>
      </div>
    );
  }

  if (variant === 'billing' && data && data?.result && data?.result?.accountDetails) {
    return (
      <div className="grid grid-flow-row-dense grid-cols-2 grid-rows-2 gap-6 sm:flex sm:flex-row justify-between w-full md:gap-0 sm:max-w-[592px]">
        <div className="flex flex-col items-center md:items-start gap-1">
          <Text
            tag="p"
            field={props.fields.data.item.AccountBalance}
            className="text-minus1 sm:text-base text-textQuattuordenary font-primaryRegular text-center md:text-left"
          />
          <Text
            tag="p"
            field={{ value: '$' + data?.result?.amount }}
            className="text-plus3 text-textPrimary font-primaryBlack md:text-plus4"
          />
        </div>
        <div className="flex flex-col gap-1">
          <div className="flex flex-row gap-1">
            <Text
              tag="p"
              field={{ value: props.fields.data.item.DueDate.value + ':' }}
              className="text-minus1 sm:text-base text-textQuattuordenary font-primaryRegular md:text-lg"
            />
            <Text
              tag="p"
              field={{
                value: dayjs(dueDate)
                  .format(
                    props?.fields?.data.item.DateFormat?.value?.length > 0
                      ? props?.fields?.data.item.DateFormat?.value
                      : 'MM/DD/YYYY'
                  )
                  .toString(),
              }}
              className="text-minus1 sm:text-base text-textQuattuordenary font-primaryBold md:text-lg"
            />
          </div>

          {/* {usageoverViewData && usageoverViewData?.result.length > 0 && (
            <div className="text-minus1 sm:text-base text-textQuattuordenary font-primaryBold md:text-lg">
              {props.fields.data.item.Usage.value}: {usageoverViewData?.result[0]?.usageToDate}{' '}
              {props.fields.data.item.Unit.value}
            </div>
          )} */}
          <MantineButton
            className="font-primaryBlack text-base mt-2 text-textSecondary decoration-textPrimary hover:decoration-textPrimary"
            onClick={() =>
              viewPDF(
                data?.result?.accountDetails?.invoiceDetails?.contentRepositoryId,
                data?.result?.accountDetails?.invoiceDetails?.documentId
              )
            }
          >
            {props.fields.data.item.ViewBill.value}{' '}
            <FontAwesomeIcon className="pl-2" icon={faFileMagnifyingGlass} />
          </MantineButton>
        </div>
        <div className="col-span-2">
          <Button className="w-full mb-4 px-4" onClick={() => makePaymentRedirection(false)}>
            {props.fields.data.item.MakeAPayment.value}
          </Button>
        </div>
      </div>
    );
  }

  return <></>;
};

export { AccountBalance };
const Component = withDatasourceCheck()<AccountBalanceProps>(AccountBalance);
export default aiLogger(Component, Component.name);
