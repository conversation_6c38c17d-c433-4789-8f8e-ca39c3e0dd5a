"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/Payment/PaymentDetails/PaymentDetails.tsx":
/*!******************************************************************!*\
  !*** ./src/components/Payment/PaymentDetails/PaymentDetails.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PaymentDetails: function() { return /* binding */ PaymentDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fortawesome/pro-regular-svg-icons */ \"./node_modules/@fortawesome/pro-regular-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/pro-solid-svg-icons */ \"./node_modules/@fortawesome/pro-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _mantine_dates__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mantine/dates */ \"./node_modules/@mantine/dates/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/hooks */ \"./node_modules/@mantine/hooks/esm/index.js\");\n/* harmony import */ var dayjs_locale_es__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs/locale/es */ \"./node_modules/dayjs/locale/es.js\");\n/* harmony import */ var dayjs_locale_es__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_es__WEBPACK_IMPORTED_MODULE_6__);\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n // Import Spanish locale\r\nconst PaymentDetails = (props)=>{\r\n    var _props_fields_DateFormat, _props_fields;\r\n    _s();\r\n    const numberInputRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\r\n    const dateInputRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\r\n    const [formattedPaymentAmount, setFormattedPaymentAmount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\r\n    const [touched, setTouched] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\r\n    const penIcon = _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faPen;\r\n    const calendarIcon = _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faCalendar;\r\n    console.log(\"props=\", props);\r\n    let selectedAccount = undefined;\r\n    if (!isPageEditing) {\r\n        selectedAccount = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_5__.useAppSelector)((state)=>{\r\n            var _state_authuser_accountSelection_contractAccount, _state_authuser_accountSelection, _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : (_state_authuser_accountSelection = _state_authuser.accountSelection) === null || _state_authuser_accountSelection === void 0 ? void 0 : (_state_authuser_accountSelection_contractAccount = _state_authuser_accountSelection.contractAccount) === null || _state_authuser_accountSelection_contractAccount === void 0 ? void 0 : _state_authuser_accountSelection_contractAccount.value;\r\n        });\r\n    }\r\n    const { isLoading, data, error, isFetching, isRefetching } = useQuery({\r\n        queryKey: [\r\n            \"accountbalance\",\r\n            selectedAccount\r\n        ],\r\n        queryFn: ()=>axios.get(\"/api/billpaycombined\", {\r\n                params: {\r\n                    accountNumber: selectedAccount\r\n                }\r\n            }).then((res)=>res.data),\r\n        enabled: !!selectedAccount\r\n    });\r\n    // Update formattedPaymentAmount when paymentAmount changes\r\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\r\n        if (props.form.values.paymentAmount !== undefined) {\r\n            setFormattedPaymentAmount(\"$ \" + props.form.values.paymentAmount.toFixed(2));\r\n        }\r\n    }, [\r\n        props.form.values.paymentAmount\r\n    ]); // Run effect when paymentAmount changes\r\n    const isSpanish = window.location.pathname.startsWith(\"/es\");\r\n    const handlePaymentAmountChange = (event)=>{\r\n        const value = event.target.value.replace(/[^0-9]/g, \"\"); // Remove non-numeric characters\r\n        const numericValue = value && value > \"0\" ? parseFloat(value) / 100 : 0.0; // Convert to cents\r\n        const formattedAmount = value !== \"\" ? \"$ \".concat(new Intl.NumberFormat(\"en-US\", {\r\n            style: \"currency\",\r\n            currency: \"USD\"\r\n        }).format(numericValue).replace(\"$\", \"\")) : \"$ 0.00\"; // Add $ symbol and remove any extra $\r\n        setFormattedPaymentAmount(formattedAmount);\r\n        props.form.setFieldValue(\"paymentAmount\", numericValue);\r\n    };\r\n    const handleBlur = (field)=>{\r\n        setTouched((prev)=>({\r\n                ...prev,\r\n                [field]: true\r\n            }));\r\n        props.form.validateField(field);\r\n        if (field === \"paymentAmount\") {\r\n            validatePaymentAmount(props.form.values);\r\n        }\r\n    };\r\n    const validatePaymentAmount = (values)=>{\r\n        if (!values.paymentAmount || values.paymentAmount <= props.minPaymentAmount) {\r\n            const minPaymentMessage = (props === null || props === void 0 ? void 0 : props.minPaymentWarning) || \"\";\r\n            props.form.setFieldError(\"paymentAmount\", minPaymentMessage);\r\n            return false;\r\n        }\r\n        props.form.clearFieldError(\"paymentAmount\");\r\n        return true;\r\n    };\r\n    const isDesktop = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery)(\"(min-width: 768px)\"); // Adjust breakpoint as needed\r\n    return (\r\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"mp_payment_fields w-full flex md:flex-row flex-row md:px-0 md:w-[590px] gap-4 md:gap-8 tee:gap-4 tee:md:gap-8 items-center sm:items-start text-left tee:text-center tee:sm:text-left\",\r\n            children: [\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_10__.TextInput, {\r\n                    label: props.fields.PaymentAmountLabel.value,\r\n                    ref: numberInputRef,\r\n                    styles: ()=>{\r\n                        return {\r\n                            root: {\r\n                                maxWidth: \"160px\",\r\n                                width: \"auto\"\r\n                            },\r\n                            input: {\r\n                                fontSize: isDesktop ? \"24px\" : \"20px\"\r\n                            },\r\n                            label: {\r\n                                fontSize: \"18px\",\r\n                                color: \"#414042\",\r\n                                fontFamily: \"OpenSans-Bold\"\r\n                            }\r\n                        };\r\n                    },\r\n                    value: formattedPaymentAmount,\r\n                    onChange: handlePaymentAmountChange,\r\n                    onBlur: ()=>handleBlur(\"paymentAmount\"),\r\n                    error: touched.paymentAmount && props.form.errors.paymentAmount,\r\n                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        className: \"cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1\",\r\n                        icon: penIcon,\r\n                        onClick: ()=>{\r\n                            numberInputRef.current && numberInputRef.current.focus();\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 130,\r\n                        columnNumber: 11\r\n                    }, void 0)\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                    lineNumber: 106,\r\n                    columnNumber: 7\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_dates__WEBPACK_IMPORTED_MODULE_11__.DateInput, {\r\n                    locale: isSpanish ? \"es\" : \"en\",\r\n                    label: props.fields.PaymentDateLabel.value,\r\n                    ref: dateInputRef,\r\n                    styles: (theme)=>{\r\n                        var _theme_other_fontFamily, _theme_other_fontFamily1, _theme_other_fontFamily2;\r\n                        return {\r\n                            root: {\r\n                                maxWidth: \"160px\",\r\n                                width: \"auto\"\r\n                            },\r\n                            label: {\r\n                                fontSize: \"14px\",\r\n                                color: \"#414042\",\r\n                                fontFamily: \"OpenSans-Bold\"\r\n                            },\r\n                            levelsGroup: {\r\n                                border: \"1px solid #004861\"\r\n                            },\r\n                            calendarHeaderLevel: {\r\n                                fontFamily: (_theme_other_fontFamily = theme.other.fontFamily) === null || _theme_other_fontFamily === void 0 ? void 0 : _theme_other_fontFamily.primaryBold[0],\r\n                                color: theme.other.colors.textUndenary[0],\r\n                                fontSize: \"18px\"\r\n                            },\r\n                            weekday: {\r\n                                fontFamily: (_theme_other_fontFamily1 = theme.other.fontFamily) === null || _theme_other_fontFamily1 === void 0 ? void 0 : _theme_other_fontFamily1.primaryBold[0],\r\n                                color: theme.other.colors.textUndenary[0]\r\n                            },\r\n                            day: {\r\n                                \"&[data-selected]\": {\r\n                                    background: \"#fff\",\r\n                                    borderBottom: \"2px solid #F26D0C\",\r\n                                    color: \"black\",\r\n                                    borderRadius: \"0\",\r\n                                    \"&:hover\": {\r\n                                        borderBottom: \"2px solid #F26D0C\",\r\n                                        background: \"#fff\",\r\n                                        color: theme.other.colors.textUndenary[0],\r\n                                        borderRadius: \"0\"\r\n                                    }\r\n                                },\r\n                                \"&[data-due-date]\": {\r\n                                    backgroundColor: \"#F26D0C !important\",\r\n                                    color: \"#fff\",\r\n                                    fontWeight: 600,\r\n                                    borderRadius: \"0\"\r\n                                },\r\n                                color: theme.other.colors.textUndenary[0],\r\n                                fontFamily: (_theme_other_fontFamily2 = theme.other.fontFamily) === null || _theme_other_fontFamily2 === void 0 ? void 0 : _theme_other_fontFamily2.primaryBold[0],\r\n                                \"&:disabled\": {\r\n                                    color: \"#87858E\",\r\n                                    fontFamily: theme.other.fontFamily.primaryRegular[0],\r\n                                    border: \"none\"\r\n                                },\r\n                                \"&:hover\": {\r\n                                    borderBottom: \"2px solid #F26D0C\",\r\n                                    color: theme.other.colors.textUndenary[0],\r\n                                    borderRadius: \"0\",\r\n                                    \"&:disabled\": {\r\n                                        color: \"#87858E\",\r\n                                        fontFamily: theme.other.fontFamily.primaryRegular[0]\r\n                                    }\r\n                                }\r\n                            }\r\n                        };\r\n                    },\r\n                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: calendarIcon,\r\n                        className: \"cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1\",\r\n                        onClick: ()=>dateInputRef.current && dateInputRef.current.focus()\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 201,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    previousIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronLeft,\r\n                        className: \"text-textPrimary hover:text-textSecondary text-plus1\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 208,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    nextIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_8__.faChevronRight,\r\n                        className: \"text-textPrimary hover:text-textSecondary text-plus1\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 214,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    minDate: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().toDate(),\r\n                    // fix\r\n                    maxDate: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().add(2, \"day\").toDate(),\r\n                    weekendDays: [],\r\n                    ...props.form.getInputProps(\"paymentDate\"),\r\n                    valueFormat: (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_DateFormat = _props_fields.DateFormat) === null || _props_fields_DateFormat === void 0 ? void 0 : _props_fields_DateFormat.value,\r\n                    getDayProps: (date)=>{\r\n                        var _props_form_values_dueDate;\r\n                        const sameDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).isSame((_props_form_values_dueDate = props.form.values.dueDate) === null || _props_form_values_dueDate === void 0 ? void 0 : _props_form_values_dueDate.replace(/T00:00:00Z/g, \"\"), \"day\");\r\n                        if (sameDate) {\r\n                            return {\r\n                                sx: (theme)=>{\r\n                                    var _theme_other_colors_bgPrimary, _theme_other_colors, _theme_other;\r\n                                    return {\r\n                                        backgroundColor: \"\".concat(theme === null || theme === void 0 ? void 0 : (_theme_other = theme.other) === null || _theme_other === void 0 ? void 0 : (_theme_other_colors = _theme_other.colors) === null || _theme_other_colors === void 0 ? void 0 : (_theme_other_colors_bgPrimary = _theme_other_colors.bgPrimary) === null || _theme_other_colors_bgPrimary === void 0 ? void 0 : _theme_other_colors_bgPrimary[0], \" !important\"),\r\n                                        color: \"white\",\r\n                                        transform: \"translate(0px, -4px)\"\r\n                                    };\r\n                                }\r\n                            };\r\n                        }\r\n                        return {};\r\n                    },\r\n                    renderDay: (date)=>{\r\n                        var _props_form_values_dueDate;\r\n                        const sameDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).isSame((_props_form_values_dueDate = props.form.values.dueDate) === null || _props_form_values_dueDate === void 0 ? void 0 : _props_form_values_dueDate.replace(/T00:00:00Z/g, \"\"), \"day\");\r\n                        const day = date.getDate();\r\n                        if (sameDate) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"flex flex-col items-center\",\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"font-primaryRegular text-minus4 translate-y-[2px]\",\r\n                                    children: \"DUE\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                                    lineNumber: 252,\r\n                                    columnNumber: 17\r\n                                }, void 0),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"translate-y-[-2px]\",\r\n                                    children: day\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                                    lineNumber: 253,\r\n                                    columnNumber: 17\r\n                                }, void 0)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                            lineNumber: 251,\r\n                            columnNumber: 15\r\n                        }, void 0);\r\n                        else {\r\n                            return day;\r\n                        }\r\n                    }\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                    lineNumber: 139,\r\n                    columnNumber: 7\r\n                }, undefined)\r\n            ]\r\n        }, void 0, true, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n            lineNumber: 105,\r\n            columnNumber: 5\r\n        }, undefined)\r\n    );\r\n};\r\n_s(PaymentDetails, \"l3ow2gSldwDS/31/MBuW/0Erw64=\", true, function() {\r\n    return [\r\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery\r\n    ];\r\n});\r\n_c = PaymentDetails;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_12__.withDatasourceCheck)()(PaymentDetails);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"PaymentDetails\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Payment/PaymentDetails/PaymentDetails.tsx\n"));

/***/ })

});