"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/Payment/PaymentDetails/PaymentDetails.tsx":
/*!******************************************************************!*\
  !*** ./src/components/Payment/PaymentDetails/PaymentDetails.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PaymentDetails: function() { return /* binding */ PaymentDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fortawesome/pro-regular-svg-icons */ \"./node_modules/@fortawesome/pro-regular-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fortawesome/pro-solid-svg-icons */ \"./node_modules/@fortawesome/pro-solid-svg-icons/index.mjs\");\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _mantine_dates__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mantine/dates */ \"./node_modules/@mantine/dates/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mantine_hooks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mantine/hooks */ \"./node_modules/@mantine/hooks/esm/index.js\");\n/* harmony import */ var dayjs_locale_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs/locale/es */ \"./node_modules/dayjs/locale/es.js\");\n/* harmony import */ var dayjs_locale_es__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_es__WEBPACK_IMPORTED_MODULE_5__);\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n // Import Spanish locale\r\nconst PaymentDetails = (props)=>{\r\n    var _props_fields_DateFormat, _props_fields;\r\n    _s();\r\n    const numberInputRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\r\n    const dateInputRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\r\n    const [formattedPaymentAmount, setFormattedPaymentAmount] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\r\n    const [touched, setTouched] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({});\r\n    const penIcon = _fortawesome_pro_solid_svg_icons__WEBPACK_IMPORTED_MODULE_6__.faPen;\r\n    const calendarIcon = _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faCalendar;\r\n    console.log(\"props=\", props);\r\n    let selectedAccount = undefined;\r\n    if (!isPageEditing) {\r\n        selectedAccount = useAppSelector((state)=>{\r\n            var _state_authuser_accountSelection_contractAccount, _state_authuser_accountSelection, _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : (_state_authuser_accountSelection = _state_authuser.accountSelection) === null || _state_authuser_accountSelection === void 0 ? void 0 : (_state_authuser_accountSelection_contractAccount = _state_authuser_accountSelection.contractAccount) === null || _state_authuser_accountSelection_contractAccount === void 0 ? void 0 : _state_authuser_accountSelection_contractAccount.value;\r\n        });\r\n        selectedAccountBPNumber = useAppSelector((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n        selectedAccountEsiid = useAppSelector((state)=>{\r\n            var _state_authuser_accountSelection_esiid, _state_authuser_accountSelection, _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : (_state_authuser_accountSelection = _state_authuser.accountSelection) === null || _state_authuser_accountSelection === void 0 ? void 0 : (_state_authuser_accountSelection_esiid = _state_authuser_accountSelection.esiid) === null || _state_authuser_accountSelection_esiid === void 0 ? void 0 : _state_authuser_accountSelection_esiid.value;\r\n        });\r\n        deferralData = useAppSelector((state)=>{\r\n            var _state_payment;\r\n            return state === null || state === void 0 ? void 0 : (_state_payment = state.payment) === null || _state_payment === void 0 ? void 0 : _state_payment.deferralData;\r\n        });\r\n    }\r\n    const { isLoading, data, error, isFetching, isRefetching } = useQuery({\r\n        queryKey: [\r\n            \"accountbalance\",\r\n            selectedAccount\r\n        ],\r\n        queryFn: ()=>axios.get(\"/api/billpaycombined\", {\r\n                params: {\r\n                    accountNumber: selectedAccount\r\n                }\r\n            }).then((res)=>res.data),\r\n        enabled: !!selectedAccount\r\n    });\r\n    // Update formattedPaymentAmount when paymentAmount changes\r\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\r\n        if (props.form.values.paymentAmount !== undefined) {\r\n            setFormattedPaymentAmount(\"$ \" + props.form.values.paymentAmount.toFixed(2));\r\n        }\r\n    }, [\r\n        props.form.values.paymentAmount\r\n    ]); // Run effect when paymentAmount changes\r\n    const isSpanish = window.location.pathname.startsWith(\"/es\");\r\n    const handlePaymentAmountChange = (event)=>{\r\n        const value = event.target.value.replace(/[^0-9]/g, \"\"); // Remove non-numeric characters\r\n        const numericValue = value && value > \"0\" ? parseFloat(value) / 100 : 0.0; // Convert to cents\r\n        const formattedAmount = value !== \"\" ? \"$ \".concat(new Intl.NumberFormat(\"en-US\", {\r\n            style: \"currency\",\r\n            currency: \"USD\"\r\n        }).format(numericValue).replace(\"$\", \"\")) : \"$ 0.00\"; // Add $ symbol and remove any extra $\r\n        setFormattedPaymentAmount(formattedAmount);\r\n        props.form.setFieldValue(\"paymentAmount\", numericValue);\r\n    };\r\n    const handleBlur = (field)=>{\r\n        setTouched((prev)=>({\r\n                ...prev,\r\n                [field]: true\r\n            }));\r\n        props.form.validateField(field);\r\n        if (field === \"paymentAmount\") {\r\n            validatePaymentAmount(props.form.values);\r\n        }\r\n    };\r\n    const validatePaymentAmount = (values)=>{\r\n        if (!values.paymentAmount || values.paymentAmount <= props.minPaymentAmount) {\r\n            const minPaymentMessage = (props === null || props === void 0 ? void 0 : props.minPaymentWarning) || \"\";\r\n            props.form.setFieldError(\"paymentAmount\", minPaymentMessage);\r\n            return false;\r\n        }\r\n        props.form.clearFieldError(\"paymentAmount\");\r\n        return true;\r\n    };\r\n    const isDesktop = (0,_mantine_hooks__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery)(\"(min-width: 768px)\"); // Adjust breakpoint as needed\r\n    return (\r\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"mp_payment_fields w-full flex md:flex-row flex-row md:px-0 md:w-[590px] gap-4 md:gap-8 tee:gap-4 tee:md:gap-8 items-center sm:items-start text-left tee:text-center tee:sm:text-left\",\r\n            children: [\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_9__.TextInput, {\r\n                    label: props.fields.PaymentAmountLabel.value,\r\n                    ref: numberInputRef,\r\n                    styles: ()=>{\r\n                        return {\r\n                            root: {\r\n                                maxWidth: \"160px\",\r\n                                width: \"auto\"\r\n                            },\r\n                            input: {\r\n                                fontSize: isDesktop ? \"24px\" : \"20px\"\r\n                            },\r\n                            label: {\r\n                                fontSize: \"18px\",\r\n                                color: \"#414042\",\r\n                                fontFamily: \"OpenSans-Bold\"\r\n                            }\r\n                        };\r\n                    },\r\n                    value: formattedPaymentAmount,\r\n                    onChange: handlePaymentAmountChange,\r\n                    onBlur: ()=>handleBlur(\"paymentAmount\"),\r\n                    error: touched.paymentAmount && props.form.errors.paymentAmount,\r\n                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        className: \"cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1\",\r\n                        icon: penIcon,\r\n                        onClick: ()=>{\r\n                            numberInputRef.current && numberInputRef.current.focus();\r\n                        }\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 135,\r\n                        columnNumber: 11\r\n                    }, void 0)\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                    lineNumber: 111,\r\n                    columnNumber: 7\r\n                }, undefined),\r\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_dates__WEBPACK_IMPORTED_MODULE_10__.DateInput, {\r\n                    locale: isSpanish ? \"es\" : \"en\",\r\n                    label: props.fields.PaymentDateLabel.value,\r\n                    ref: dateInputRef,\r\n                    styles: (theme)=>{\r\n                        var _theme_other_fontFamily, _theme_other_fontFamily1, _theme_other_fontFamily2;\r\n                        return {\r\n                            root: {\r\n                                maxWidth: \"160px\",\r\n                                width: \"auto\"\r\n                            },\r\n                            label: {\r\n                                fontSize: \"14px\",\r\n                                color: \"#414042\",\r\n                                fontFamily: \"OpenSans-Bold\"\r\n                            },\r\n                            levelsGroup: {\r\n                                border: \"1px solid #004861\"\r\n                            },\r\n                            calendarHeaderLevel: {\r\n                                fontFamily: (_theme_other_fontFamily = theme.other.fontFamily) === null || _theme_other_fontFamily === void 0 ? void 0 : _theme_other_fontFamily.primaryBold[0],\r\n                                color: theme.other.colors.textUndenary[0],\r\n                                fontSize: \"18px\"\r\n                            },\r\n                            weekday: {\r\n                                fontFamily: (_theme_other_fontFamily1 = theme.other.fontFamily) === null || _theme_other_fontFamily1 === void 0 ? void 0 : _theme_other_fontFamily1.primaryBold[0],\r\n                                color: theme.other.colors.textUndenary[0]\r\n                            },\r\n                            day: {\r\n                                \"&[data-selected]\": {\r\n                                    background: \"#fff\",\r\n                                    borderBottom: \"2px solid #F26D0C\",\r\n                                    color: \"black\",\r\n                                    borderRadius: \"0\",\r\n                                    \"&:hover\": {\r\n                                        borderBottom: \"2px solid #F26D0C\",\r\n                                        background: \"#fff\",\r\n                                        color: theme.other.colors.textUndenary[0],\r\n                                        borderRadius: \"0\"\r\n                                    }\r\n                                },\r\n                                \"&[data-due-date]\": {\r\n                                    backgroundColor: \"#F26D0C !important\",\r\n                                    color: \"#fff\",\r\n                                    fontWeight: 600,\r\n                                    borderRadius: \"0\"\r\n                                },\r\n                                color: theme.other.colors.textUndenary[0],\r\n                                fontFamily: (_theme_other_fontFamily2 = theme.other.fontFamily) === null || _theme_other_fontFamily2 === void 0 ? void 0 : _theme_other_fontFamily2.primaryBold[0],\r\n                                \"&:disabled\": {\r\n                                    color: \"#87858E\",\r\n                                    fontFamily: theme.other.fontFamily.primaryRegular[0],\r\n                                    border: \"none\"\r\n                                },\r\n                                \"&:hover\": {\r\n                                    borderBottom: \"2px solid #F26D0C\",\r\n                                    color: theme.other.colors.textUndenary[0],\r\n                                    borderRadius: \"0\",\r\n                                    \"&:disabled\": {\r\n                                        color: \"#87858E\",\r\n                                        fontFamily: theme.other.fontFamily.primaryRegular[0]\r\n                                    }\r\n                                }\r\n                            }\r\n                        };\r\n                    },\r\n                    rightSection: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: calendarIcon,\r\n                        className: \"cursor-pointer ml-auto mr-4 text-textPrimary hover:text-textSecondary text-plus1\",\r\n                        onClick: ()=>dateInputRef.current && dateInputRef.current.focus()\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 206,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    previousIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronLeft,\r\n                        className: \"text-textPrimary hover:text-textSecondary text-plus1\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 213,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    nextIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_1__.FontAwesomeIcon, {\r\n                        icon: _fortawesome_pro_regular_svg_icons__WEBPACK_IMPORTED_MODULE_7__.faChevronRight,\r\n                        className: \"text-textPrimary hover:text-textSecondary text-plus1\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                        lineNumber: 219,\r\n                        columnNumber: 11\r\n                    }, void 0),\r\n                    minDate: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().toDate(),\r\n                    // fix\r\n                    maxDate: dayjs__WEBPACK_IMPORTED_MODULE_2___default()().add(2, \"day\").toDate(),\r\n                    weekendDays: [],\r\n                    ...props.form.getInputProps(\"paymentDate\"),\r\n                    valueFormat: (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_DateFormat = _props_fields.DateFormat) === null || _props_fields_DateFormat === void 0 ? void 0 : _props_fields_DateFormat.value,\r\n                    getDayProps: (date)=>{\r\n                        var _props_form_values_dueDate;\r\n                        const sameDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).isSame((_props_form_values_dueDate = props.form.values.dueDate) === null || _props_form_values_dueDate === void 0 ? void 0 : _props_form_values_dueDate.replace(/T00:00:00Z/g, \"\"), \"day\");\r\n                        if (sameDate) {\r\n                            return {\r\n                                sx: (theme)=>{\r\n                                    var _theme_other_colors_bgPrimary, _theme_other_colors, _theme_other;\r\n                                    return {\r\n                                        backgroundColor: \"\".concat(theme === null || theme === void 0 ? void 0 : (_theme_other = theme.other) === null || _theme_other === void 0 ? void 0 : (_theme_other_colors = _theme_other.colors) === null || _theme_other_colors === void 0 ? void 0 : (_theme_other_colors_bgPrimary = _theme_other_colors.bgPrimary) === null || _theme_other_colors_bgPrimary === void 0 ? void 0 : _theme_other_colors_bgPrimary[0], \" !important\"),\r\n                                        color: \"white\",\r\n                                        transform: \"translate(0px, -4px)\"\r\n                                    };\r\n                                }\r\n                            };\r\n                        }\r\n                        return {};\r\n                    },\r\n                    renderDay: (date)=>{\r\n                        var _props_form_values_dueDate;\r\n                        const sameDate = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).isSame((_props_form_values_dueDate = props.form.values.dueDate) === null || _props_form_values_dueDate === void 0 ? void 0 : _props_form_values_dueDate.replace(/T00:00:00Z/g, \"\"), \"day\");\r\n                        const day = date.getDate();\r\n                        if (sameDate) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"flex flex-col items-center\",\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"font-primaryRegular text-minus4 translate-y-[2px]\",\r\n                                    children: \"DUE\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                                    lineNumber: 257,\r\n                                    columnNumber: 17\r\n                                }, void 0),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"translate-y-[-2px]\",\r\n                                    children: day\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                                    lineNumber: 258,\r\n                                    columnNumber: 17\r\n                                }, void 0)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                            lineNumber: 256,\r\n                            columnNumber: 15\r\n                        }, void 0);\r\n                        else {\r\n                            return day;\r\n                        }\r\n                    }\r\n                }, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n                    lineNumber: 144,\r\n                    columnNumber: 7\r\n                }, undefined)\r\n            ]\r\n        }, void 0, true, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\Payment\\\\PaymentDetails\\\\PaymentDetails.tsx\",\r\n            lineNumber: 110,\r\n            columnNumber: 5\r\n        }, undefined)\r\n    );\r\n};\r\n_s(PaymentDetails, \"l3ow2gSldwDS/31/MBuW/0Erw64=\", true, function() {\r\n    return [\r\n        _mantine_hooks__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery\r\n    ];\r\n});\r\n_c = PaymentDetails;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_11__.withDatasourceCheck)()(PaymentDetails);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"PaymentDetails\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Payment/PaymentDetails/PaymentDetails.tsx\n"));

/***/ })

});