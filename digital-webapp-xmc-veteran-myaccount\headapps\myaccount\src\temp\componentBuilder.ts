/* eslint-disable */
// Do not edit this file, it is auto-generated at build time!
// See scripts/generate-component-builder/index.ts to modify the generation of this file.

import dynamic from 'next/dynamic';
import { ComponentBuilder } from '@sitecore-jss/sitecore-jss-nextjs';

import { <PERSON><PERSON><PERSON><PERSON>rapper, FEaaSWrapper } from '@sitecore-jss/sitecore-jss-nextjs';

import * as CdpPageView from 'src/components/CdpPageView';
import * as ColumnSplitter from 'src/components/ColumnSplitter';
import * as Container from 'src/components/Container';
import * as FEAASScripts from 'src/components/FEAASScripts';
import * as Image from 'src/components/Image';
import * as LinkList from 'src/components/LinkList';
import * as Navigation from 'src/components/Navigation';
import * as PageContent from 'src/components/PageContent';
import * as PartialDesignDynamicPlaceholder from 'src/components/PartialDesignDynamicPlaceholder';
import * as Promo from 'src/components/Promo';
import * as RichText from 'src/components/RichText';
import * as RowSplitter from 'src/components/RowSplitter';
import * as Title from 'src/components/Title';
import * as AdditionalProducts from 'src/components/AdditionalProducts/AdditionalProducts';
import * as AddressTypeAhead from 'src/components/AddressTypeAhead/AddressTypeAhead';
import * as AnonymousBillPay from 'src/components/Anonymous/AnonymousBillPay';
import * as AnonymousBillConfirmation from 'src/components/Anonymous/Confirmation/AnonymousBillConfirmation';
import * as TermsAndConditions from 'src/components/AutoPay/TermsAndConditions/TermsAndConditions';
import * as AccountInformation from 'src/components/COA/AccountInformation';
import * as COAConfirmation from 'src/components/COA/confirmation/COAConfirmation';
import * as Logininformation from 'src/components/COA/logininfo/Logininformation';
import * as Verifylogininformation from 'src/components/COA/verification/Verifylogininformation';
import * as AccountSelector from 'src/components/common/AccountSelector/AccountSelector';
import * as AdobeDataLayer from 'src/components/common/AdobeDataLayer/AdobeDataLayer';
import * as AMBFooter from 'src/components/common/AMBFooter/AMBFooter';
import * as BarChart from 'src/components/common/BarChart/BarChart';
import * as BarLineChart from 'src/components/common/BarLineChart/BarLineChart';
import * as Calendar from 'src/components/common/Calendar/Calendar';
import * as Carousel from 'src/components/common/Carousel/Carousel';
import * as CaseManagementWidget from 'src/components/common/CaseManagementWidget/CaseManagementWidget';
import * as UsageBar from 'src/components/common/Charts/UsageBar/UsageBar';
import * as UsagePie from 'src/components/common/Charts/UsagePie/UsagePie';
import * as GenesysChat from 'src/components/common/Chat/GenesysChat/GenesysChat';
import * as componentprops from 'src/components/common/Chat/GenesysChat/componentprops/componentprops';
import * as GenesysChatToggleLink from 'src/components/common/Chat/GenesysChat/elements/GenesysChatToggleLink';
import * as GenesysChatToggleLinkMobile from 'src/components/common/Chat/GenesysChat/elements/GenesysChatToggleLinkMobile';
import * as chatfunctions from 'src/components/common/Chat/GenesysChat/functions/chatfunctions';
import * as ConfirmationModal from 'src/components/common/ConfirmationModal/ConfirmationModal';
import * as CookieConsentBanner from 'src/components/common/CookieConsentBanner/CookieConsentBanner';
import * as CustomProgressBar from 'src/components/common/CustomProgressBar/CustomProgressBar';
import * as Divider from 'src/components/common/Divider/Divider';
import * as DividerSecondary from 'src/components/common/DividerSecondary/DividerSecondary';
import * as DynamicHTML from 'src/components/common/DynamicHTML/DynamicHTML';
import * as EditableNumberBox from 'src/components/common/EditableNumberBox/EditableNumberBox';
import * as EditablePassword from 'src/components/common/EditablePassword/EditablePassword';
import * as EditableSelect from 'src/components/common/EditableSelect/EditableSelect';
import * as EditableTextBox from 'src/components/common/EditableTextBox/EditableTextBox';
import * as EditableTextForm from 'src/components/common/EditableTextForm/EditableTextForm';
import * as EmptyBarLineChart from 'src/components/common/EmptyBarLineChart/EmptyBarLineChart';
import * as ExitPopup from 'src/components/common/ExitPopup/ExitPopup';
import * as InfoText from 'src/components/common/InfoText/InfoText';
import * as LanguageSelector from 'src/components/common/LanguageSelector/LanguageSelector';
import * as LanguageSelectorMobile from 'src/components/common/LanguageSelectorMobile/LanguageSelectorMobile';
import * as Avatar from 'src/components/common/LeftMenu/Avatar/Avatar';
import * as LeftMenuContainer from 'src/components/common/LeftMenu/LeftMenuContainer/LeftMenuContainer';
import * as MenuList from 'src/components/common/LeftMenu/MenuList/MenuList';
import * as MobileMenu from 'src/components/common/LeftMenu/MobileMenu/MobileMenu';
import * as SiteLogo from 'src/components/common/LeftMenu/SiteLogo/SiteLogo';
import * as LeftNavigation from 'src/components/common/LeftNavigation/LeftNavigation';
import * as LineChart from 'src/components/common/LineChart/LineChart';
import * as Loader from 'src/components/common/Loader/Loader';
import * as LoaderModal from 'src/components/common/LoaderModal/LoaderModal';
import * as Oops from 'src/components/common/Oops/Oops';
import * as PageBuilder from 'src/components/common/PageBuilder/PageBuilder';
import * as PaymentAddedModal from 'src/components/common/PaymentAddedModal/PaymentAddedModal';
import * as PaymentDeletedModal from 'src/components/common/PaymentDeletedModal/PaymentDeletedModal';
import * as PrintSelectedPlanCard from 'src/components/common/PrintSelectedPlanCard/PrintSelectedPlanCard';
import * as ProgressBar from 'src/components/common/ProgressBar/ProgressBar';
import * as RenewalBanner from 'src/components/common/RenewalBanner/RenewalBanner';
import * as SalesforceLiveChat from 'src/components/common/SalesforceLiveChat/SalesforceLiveChat';
import * as SelectedPlanCard from 'src/components/common/SelectedPlanCard/SelectedPlanCard';
import * as SiteMetaData from 'src/components/common/SiteMetaData/SiteMetaData';
import * as Stepper from 'src/components/common/Stepper/Stepper';
import * as TEEAnonymousMenu from 'src/components/common/TEEAnonymousMenu/TEEAnonymousMenu';
import * as TEEFooter from 'src/components/common/TEEFooter/TEEFooter';
import * as TEEHeader from 'src/components/common/TEEHeader/TEEHeader';
import * as TwoColumnSplitter from 'src/components/common/TwoColumnSplitter/TwoColumnSplitter';
import * as TXUFooter from 'src/components/common/TXUFooter/TXUFooter';
import * as TXUHeader from 'src/components/common/TXUHeader/TXUHeader';
import * as TXUTopHeader from 'src/components/common/TXUTopHeader/TXUTopHeader';
import * as XRouteCookie from 'src/components/common/XRouteCookie/XRouteCookie';
import * as YesNoButton from 'src/components/common/YesNoButton/YesNoButton';
import * as ContentBlock from 'src/components/ContentBlock/ContentBlock';
import * as DocViewer from 'src/components/DocViewer/DocViewer';
import * as DunningLevelPastDue from 'src/components/DunningLevel/DunningLevelPastDue/DunningLevelPastDue';
import * as DunningLevelPending from 'src/components/DunningLevel/DunningLevelPending/DunningLevelPending';
import * as Button from 'src/components/Elements/Button/Button';
import * as Incentive from 'src/components/Elements/Incentive/Incentive';
import * as Input from 'src/components/Elements/Input/Input';
import * as Link from 'src/components/Elements/Link/Link';
import * as PrintPageButton from 'src/components/Elements/PrintPageButton/PrintPageButton';
import * as Select from 'src/components/Elements/Select/Select';
import * as Tooltip from 'src/components/Elements/Tooltip/Tooltip';
import * as ErrorBoundary from 'src/components/ErrorBoundary/ErrorBoundary';
import * as StyleguideFieldUsageCheckbox from 'src/components/fields/Styleguide-FieldUsage-Checkbox';
import * as StyleguideFieldUsageContentList from 'src/components/fields/Styleguide-FieldUsage-ContentList';
import * as StyleguideFieldUsageCustom from 'src/components/fields/Styleguide-FieldUsage-Custom';
import * as StyleguideFieldUsageDate from 'src/components/fields/Styleguide-FieldUsage-Date';
import * as StyleguideFieldUsageFile from 'src/components/fields/Styleguide-FieldUsage-File';
import * as StyleguideFieldUsageImage from 'src/components/fields/Styleguide-FieldUsage-Image';
import * as StyleguideFieldUsageItemLink from 'src/components/fields/Styleguide-FieldUsage-ItemLink';
import * as StyleguideFieldUsageLink from 'src/components/fields/Styleguide-FieldUsage-Link';
import * as StyleguideFieldUsageNumber from 'src/components/fields/Styleguide-FieldUsage-Number';
import * as StyleguideFieldUsageRichText from 'src/components/fields/Styleguide-FieldUsage-RichText';
import * as StyleguideFieldUsageText from 'src/components/fields/Styleguide-FieldUsage-Text';
const GraphQLConnectedDemo = {
  module: () => import('src/components/graphql/GraphQL-ConnectedDemo.dynamic'),
  element: (isEditing?: boolean) => isEditing ? require('src/components/graphql/GraphQL-ConnectedDemo.dynamic')?.default : dynamic(GraphQLConnectedDemo.module)
}
import * as GraphQLIntegratedDemo from 'src/components/graphql/GraphQL-IntegratedDemo';
import * as GraphQLLayout from 'src/components/graphql/GraphQL-Layout';
import * as Loading from 'src/components/Loading/Loading';
import * as Maintenance from 'src/components/MaintenanceSettings/Maintenance';
import * as MessageContainer from 'src/components/Messages/MessageContainer/MessageContainer';
import * as MessageFilter from 'src/components/Messages/MessageFilter/MessageFilter';
import * as MessageList from 'src/components/Messages/MessageList/MessageList';
import * as DunningDisconnected from 'src/components/MyAccount/DunningDisconnected/DunningDisconnected';
import * as DunningLevel30 from 'src/components/MyAccount/DunningLevel30/DunningLevel30';
import * as DunningLevel50 from 'src/components/MyAccount/DunningLevel50/DunningLevel50';
import * as DunningLevelMessages from 'src/components/MyAccount/DunningLevelMessages/DunningLevelMessages';
import * as ForgotPassword from 'src/components/MyAccount/ForgotPassword/ForgotPassword';
import * as ForgotUserName from 'src/components/MyAccount/ForgotUserName/ForgotUserName';
import * as Login from 'src/components/MyAccount/Login/Login';
import * as Logout from 'src/components/MyAccount/Logout/Logout';
import * as AccountBalance from 'src/components/MyAccount/MiniWidgets/AccountBalance/AccountBalance';
import * as DeferredPaymentStatus from 'src/components/MyAccount/MiniWidgets/DeferredPaymentStatus/DeferredPaymentStatus';
import * as EstimatedBill from 'src/components/MyAccount/MiniWidgets/EstimatedBill/EstimatedBill';
import * as ForecastedBill from 'src/components/MyAccount/MiniWidgets/ForecastedBill/ForecastedBill';
import * as ProductWidget from 'src/components/MyAccount/MiniWidgets/ProductWidget/ProductWidget';
import * as DaytimePass from 'src/components/MyAccount/MiniWidgets/ProductWidgets/DaytimePass/DaytimePass';
import * as FreeEVMiles from 'src/components/MyAccount/MiniWidgets/ProductWidgets/FreeEVMiles/FreeEVMiles';
import * as LiveYourFree from 'src/components/MyAccount/MiniWidgets/ProductWidgets/LiveYourFree/LiveYourFree';
import * as RenewalBuyBack from 'src/components/MyAccount/MiniWidgets/ProductWidgets/RenewalBuyBack/RenewalBuyBack';
import * as SeasonPass from 'src/components/MyAccount/MiniWidgets/ProductWidgets/SeasonPass/SeasonPass';
import * as SolarBuybackMatch from 'src/components/MyAccount/MiniWidgets/ProductWidgets/SolarBuybackMatch/SolarBuybackMatch';
import * as UltimateSummerPass from 'src/components/MyAccount/MiniWidgets/ProductWidgets/UltimateSummerPass/UltimateSummerPass';
import * as RenewMyPlan from 'src/components/MyAccount/MiniWidgets/RenewMyPlan/RenewMyPlan';
import * as RewardsWidget from 'src/components/MyAccount/MiniWidgets/RewardsWidget/RewardsWidget';
import * as UsagetoDate from 'src/components/MyAccount/MiniWidgets/UsagetoDate/UsagetoDate';
import * as WeeklyComparison from 'src/components/MyAccount/MiniWidgets/WeeklyComparison/WeeklyComparison';
import * as GreenupWidget from 'src/components/MyAccount/ProductWidgetContainer/GreenupWidget';
import * as ProductWidgetContainer from 'src/components/MyAccount/ProductWidgetContainer/ProductWidgetContainer';
import * as SummaryContainer from 'src/components/MyAccount/SummaryContainer/SummaryContainer';
import * as TEEForgotPassword from 'src/components/MyAccount/TEEForgotPassword/TEEForgotPassword';
import * as UsageBreakdown from 'src/components/MyAccount/UsageBreakdown/UsageBreakdown';
import * as UsageGraph from 'src/components/MyAccount/UsageGraph/UsageGraph';
import * as UsageOverviewContainer from 'src/components/MyAccount/UsageOverviewContainer/UsageOverviewContainer';
import * as CharityWidget from 'src/components/MyCharity/CharityWidget';
import * as MyCharity from 'src/components/MyCharity/MyCharity';
import * as types from 'src/components/MyCharity/types';
import * as ComponentProps from 'src/components/MyCharity/ComponentProps/ComponentProps';
import * as CharityItem from 'src/components/MyCharity/elements/CharityItem';
import * as SelectedCharity from 'src/components/MyCharity/elements/SelectedCharity';
import * as myCharity from 'src/components/MyCharity/functions/myCharity';
import * as MyEnergyDashboardContainer from 'src/components/MyEnergyDashBoard/MyEnergyDashboardContainer/MyEnergyDashboardContainer';
import * as CompareBill from 'src/components/MyEnergyDashBoard/Widgets/CompareBill/CompareBill';
import * as DailyUsageGraph from 'src/components/MyEnergyDashBoard/Widgets/DailyUsageGraph/DailyUsageGraph';
import * as HomeComparison from 'src/components/MyEnergyDashBoard/Widgets/HomeComparison/HomeComparison';
import * as HourlyUsageGraph from 'src/components/MyEnergyDashBoard/Widgets/HourlyUsageGraph/HourlyUsageGraph';
import * as MonthlyUsageGraph from 'src/components/MyEnergyDashBoard/Widgets/MonthlyUsageGraph/MonthlyUsageGraph';
import * as AddonPlanCard from 'src/components/MyPlanAndProducts/AddonPlanCard/AddonPlanCard';
import * as ExtendedAddonPlanCard from 'src/components/MyPlanAndProducts/AddonPlanCard/ExtendedAddonPlanCard';
import * as AddonPlanCardList from 'src/components/MyPlanAndProducts/AddonPlanCardList/AddonPlanCardList';
import * as MyPlan from 'src/components/MyPlanAndProducts/MyPlan/MyPlan';
import * as MyPlanAndProductsContainer from 'src/components/MyPlanAndProducts/MyPlanAndProductsContainer/MyPlanAndProductsContainer';
import * as MyPlansContainer from 'src/components/MyPlanAndProducts/MyPlansContainer/MyPlansContainer';
import * as MyProducts from 'src/components/MyPlanAndProducts/MyProducts/MyProducts';
import * as MyReferral from 'src/components/MyReferral/MyReferral';
import * as FreeEnergyRewards from 'src/components/MyReferral/FreeEnergyRewards/FreeEnergyRewards';
import * as NonCommodityProductPlanCard from 'src/components/NonCommodityProductPlanCard/NonCommodityProductPlanCard';
import * as NonCommodityProducts from 'src/components/NonCommodityProducts/NonCommodityProducts';
import * as NotificationBanner from 'src/components/NotificationBanner/NotificationBanner';
import * as PageBanner from 'src/components/PageBanner/PageBanner';
import * as AddBank from 'src/components/Payment/AddBank/AddBank';
import * as AddCard from 'src/components/Payment/AddCard/AddCard';
import * as BillPaymentHistory from 'src/components/Payment/BillPaymentHistory/BillPaymentHistory/BillPaymentHistory';
import * as BillPaymentRewardsHistoryContainer from 'src/components/Payment/BillPaymentHistory/BillPaymentRewardsHistoryContainer/BillPaymentRewardsHistoryContainer';
import * as CancelScheduledPayment from 'src/components/Payment/BillPaymentHistory/CancelScheduledPayment/CancelScheduledPayment';
import * as ConfirmCancelScheduledPayment from 'src/components/Payment/BillPaymentHistory/ConfirmCancelScheduledPayment/ConfirmCancelScheduledPayment';
import * as RedeemRewards from 'src/components/Payment/BillPaymentHistory/RedeemRewards/RedeemRewards';
import * as RewardsHistory from 'src/components/Payment/BillPaymentHistory/RewardsHistory/RewardsHistory';
import * as AccordionList from 'src/components/Payment/Common/AccordionList/AccordionList';
import * as PaymentConfirmationInfo from 'src/components/Payment/Common/PaymentConfirmationInfo/PaymentConfirmationInfo';
import * as ReviewPaymentInfo from 'src/components/Payment/Common/ReviewPaymentInfo/ReviewPaymentInfo';
import * as ChoosePaymentMethodContainer from 'src/components/Payment/Deferral/ChoosePaymentMethodContainer/ChoosePaymentMethodContainer';
import * as DeferralConfirmation from 'src/components/Payment/Deferral/DeferralConfirmation/DeferralConfirmation';
import * as PickADate from 'src/components/Payment/Deferral/PickADate/PickADate';
import * as ReviewDeferralRequest from 'src/components/Payment/Deferral/ReviewDeferralRequest/ReviewDeferralRequest';
import * as ReviewDeferralRequestAndPay from 'src/components/Payment/Deferral/ReviewDeferralRequestAndPay/ReviewDeferralRequestAndPay';
import * as PaymentConfirmation from 'src/components/Payment/PaymentConfirmation/PaymentConfirmation';
import * as PaymentContainer from 'src/components/Payment/PaymentContainer/PaymentContainer';
import * as PaymentDetails from 'src/components/Payment/PaymentDetails/PaymentDetails';
import * as PaymentMethod from 'src/components/Payment/PaymentMethod/PaymentMethod';
import * as ReviewPayment from 'src/components/Payment/ReviewPayment/ReviewPayment';
import * as SplitPaymentAmount from 'src/components/Payment/SplitPaymentAmount/SplitPaymentAmount';
import * as SplitPaymentMethod from 'src/components/Payment/SplitPaymentMethod/SplitPaymentMethod';
import * as SplitPaymentSelectContainer from 'src/components/Payment/SplitPaymentSelectContainer/SplitPaymentSelectContainer';
import * as PaymetricIntegration from 'src/components/PaymetricIntegration/PaymetricIntegration';
import * as PdfViewer from 'src/components/PdfViewer/PdfViewer';
import * as ExtendedPlanCard from 'src/components/PlanCard/ExtendedPlanCard';
import * as PlanCard from 'src/components/PlanCard/PlanCard';
import * as RecentActivity from 'src/components/RecentActivity/RecentActivity';
import * as ReportOutage from 'src/components/ReportOutage/ReportOutage';
import * as RoutingTypeAhead from 'src/components/RoutingTypeAhead/RoutingTypeAhead';
import * as AddressDetails from 'src/components/SettingsAndPreferences/AddressDetails/AddressDetails';
import * as Autopay from 'src/components/SettingsAndPreferences/Autopay/Autopay';
import * as useAccountBalance from 'src/components/SettingsAndPreferences/Autopay/hook/useAccountBalance';
import * as AverageMonthlyBilling from 'src/components/SettingsAndPreferences/AverageMonthlyBilling/AverageMonthlyBilling';
import * as CommunicationPreferences from 'src/components/SettingsAndPreferences/CommunicationPreferences/CommunicationPreferences';
import * as CommunicationPreferenceToggle from 'src/components/SettingsAndPreferences/CommunicationPreferences/CommunicationPreferenceToggle';
import * as CustomerInformation from 'src/components/SettingsAndPreferences/CustomerInformation/CustomerInformation';
import * as EditBank from 'src/components/SettingsAndPreferences/EditBank/EditBank';
import * as EditCard from 'src/components/SettingsAndPreferences/EditCard/EditCard';
import * as LoginInformation from 'src/components/SettingsAndPreferences/LoginInformation/LoginInformation';
import * as ManagePaymentMethods from 'src/components/SettingsAndPreferences/ManagePaymentMethods/ManagePaymentMethods';
import * as PaperlessBilling from 'src/components/SettingsAndPreferences/PaperlessBilling/PaperlessBilling';
import * as SettingsAndPreferencesContainer from 'src/components/SettingsAndPreferences/SettingsAndPreferencesContainer/SettingsAndPreferencesContainer';
import * as StyleguideComponentParams from 'src/components/styleguide/Styleguide-ComponentParams';
import * as StyleguideCustomRouteType from 'src/components/styleguide/Styleguide-CustomRouteType';
import * as StyleguideLayoutReuse from 'src/components/styleguide/Styleguide-Layout-Reuse';
import * as StyleguideLayoutTabsTab from 'src/components/styleguide/Styleguide-Layout-Tabs-Tab';
import * as StyleguideLayoutTabs from 'src/components/styleguide/Styleguide-Layout-Tabs';
import * as StyleguideLayout from 'src/components/styleguide/Styleguide-Layout';
import * as StyleguideMultilingual from 'src/components/styleguide/Styleguide-Multilingual';
import * as StyleguideRouteFields from 'src/components/styleguide/Styleguide-RouteFields';
import * as StyleguideSection from 'src/components/styleguide/Styleguide-Section';
import * as StyleguideSitecoreContext from 'src/components/styleguide/Styleguide-SitecoreContext';
import * as StyleguideSpecimen from 'src/components/styleguide/Styleguide-Specimen';
import * as StyleguideTracking from 'src/components/styleguide/Styleguide-Tracking';
import * as SunRunPlanCard from 'src/components/SunRunPlanCard/SunRunPlanCard';
import * as SunRunPlanCardList from 'src/components/SunRunPlanCardList/SunRunPlanCardList';
import * as NCPlanTermsText from 'src/components/TermsAndConditions/NCPlanTermsText/NCPlanTermsText';
import * as TermsAndConditionsContainer from 'src/components/TermsAndConditions/TermsAndConditionsContainer/TermsAndConditionsContainer';

export const components = new Map();
components.set('BYOCWrapper', BYOCWrapper);
components.set('FEaaSWrapper', FEaaSWrapper);

components.set('CdpPageView', CdpPageView);
components.set('ColumnSplitter', ColumnSplitter);
components.set('Container', Container);
components.set('FEAASScripts', FEAASScripts);
components.set('Image', Image);
components.set('LinkList', LinkList);
components.set('Navigation', Navigation);
components.set('PageContent', PageContent);
components.set('PartialDesignDynamicPlaceholder', PartialDesignDynamicPlaceholder);
components.set('Promo', Promo);
components.set('RichText', RichText);
components.set('RowSplitter', RowSplitter);
components.set('Title', Title);
components.set('AdditionalProducts', AdditionalProducts);
components.set('AddressTypeAhead', AddressTypeAhead);
components.set('AnonymousBillPay', AnonymousBillPay);
components.set('AnonymousBillConfirmation', AnonymousBillConfirmation);
components.set('TermsAndConditions', TermsAndConditions);
components.set('AccountInformation', AccountInformation);
components.set('COAConfirmation', COAConfirmation);
components.set('Logininformation', Logininformation);
components.set('Verifylogininformation', Verifylogininformation);
components.set('AccountSelector', AccountSelector);
components.set('AdobeDataLayer', AdobeDataLayer);
components.set('AMBFooter', AMBFooter);
components.set('BarChart', BarChart);
components.set('BarLineChart', BarLineChart);
components.set('Calendar', Calendar);
components.set('Carousel', Carousel);
components.set('CaseManagementWidget', CaseManagementWidget);
components.set('UsageBar', UsageBar);
components.set('UsagePie', UsagePie);
components.set('GenesysChat', GenesysChat);
components.set('componentprops', componentprops);
components.set('GenesysChatToggleLink', GenesysChatToggleLink);
components.set('GenesysChatToggleLinkMobile', GenesysChatToggleLinkMobile);
components.set('chatfunctions', chatfunctions);
components.set('ConfirmationModal', ConfirmationModal);
components.set('CookieConsentBanner', CookieConsentBanner);
components.set('CustomProgressBar', CustomProgressBar);
components.set('Divider', Divider);
components.set('DividerSecondary', DividerSecondary);
components.set('DynamicHTML', DynamicHTML);
components.set('EditableNumberBox', EditableNumberBox);
components.set('EditablePassword', EditablePassword);
components.set('EditableSelect', EditableSelect);
components.set('EditableTextBox', EditableTextBox);
components.set('EditableTextForm', EditableTextForm);
components.set('EmptyBarLineChart', EmptyBarLineChart);
components.set('ExitPopup', ExitPopup);
components.set('InfoText', InfoText);
components.set('LanguageSelector', LanguageSelector);
components.set('LanguageSelectorMobile', LanguageSelectorMobile);
components.set('Avatar', Avatar);
components.set('LeftMenuContainer', LeftMenuContainer);
components.set('MenuList', MenuList);
components.set('MobileMenu', MobileMenu);
components.set('SiteLogo', SiteLogo);
components.set('LeftNavigation', LeftNavigation);
components.set('LineChart', LineChart);
components.set('Loader', Loader);
components.set('LoaderModal', LoaderModal);
components.set('Oops', Oops);
components.set('PageBuilder', PageBuilder);
components.set('PaymentAddedModal', PaymentAddedModal);
components.set('PaymentDeletedModal', PaymentDeletedModal);
components.set('PrintSelectedPlanCard', PrintSelectedPlanCard);
components.set('ProgressBar', ProgressBar);
components.set('RenewalBanner', RenewalBanner);
components.set('SalesforceLiveChat', SalesforceLiveChat);
components.set('SelectedPlanCard', SelectedPlanCard);
components.set('SiteMetaData', SiteMetaData);
components.set('Stepper', Stepper);
components.set('TEEAnonymousMenu', TEEAnonymousMenu);
components.set('TEEFooter', TEEFooter);
components.set('TEEHeader', TEEHeader);
components.set('TwoColumnSplitter', TwoColumnSplitter);
components.set('TXUFooter', TXUFooter);
components.set('TXUHeader', TXUHeader);
components.set('TXUTopHeader', TXUTopHeader);
components.set('XRouteCookie', XRouteCookie);
components.set('YesNoButton', YesNoButton);
components.set('ContentBlock', ContentBlock);
components.set('DocViewer', DocViewer);
components.set('DunningLevelPastDue', DunningLevelPastDue);
components.set('DunningLevelPending', DunningLevelPending);
components.set('Button', Button);
components.set('Incentive', Incentive);
components.set('Input', Input);
components.set('Link', Link);
components.set('PrintPageButton', PrintPageButton);
components.set('Select', Select);
components.set('Tooltip', Tooltip);
components.set('ErrorBoundary', ErrorBoundary);
components.set('Styleguide-FieldUsage-Checkbox', StyleguideFieldUsageCheckbox);
components.set('Styleguide-FieldUsage-ContentList', StyleguideFieldUsageContentList);
components.set('Styleguide-FieldUsage-Custom', StyleguideFieldUsageCustom);
components.set('Styleguide-FieldUsage-Date', StyleguideFieldUsageDate);
components.set('Styleguide-FieldUsage-File', StyleguideFieldUsageFile);
components.set('Styleguide-FieldUsage-Image', StyleguideFieldUsageImage);
components.set('Styleguide-FieldUsage-ItemLink', StyleguideFieldUsageItemLink);
components.set('Styleguide-FieldUsage-Link', StyleguideFieldUsageLink);
components.set('Styleguide-FieldUsage-Number', StyleguideFieldUsageNumber);
components.set('Styleguide-FieldUsage-RichText', StyleguideFieldUsageRichText);
components.set('Styleguide-FieldUsage-Text', StyleguideFieldUsageText);
components.set('GraphQL-ConnectedDemo', GraphQLConnectedDemo);
components.set('GraphQL-IntegratedDemo', GraphQLIntegratedDemo);
components.set('GraphQL-Layout', GraphQLLayout);
components.set('Loading', Loading);
components.set('Maintenance', Maintenance);
components.set('MessageContainer', MessageContainer);
components.set('MessageFilter', MessageFilter);
components.set('MessageList', MessageList);
components.set('DunningDisconnected', DunningDisconnected);
components.set('DunningLevel30', DunningLevel30);
components.set('DunningLevel50', DunningLevel50);
components.set('DunningLevelMessages', DunningLevelMessages);
components.set('ForgotPassword', ForgotPassword);
components.set('ForgotUserName', ForgotUserName);
components.set('Login', Login);
components.set('Logout', Logout);
components.set('AccountBalance', AccountBalance);
components.set('DeferredPaymentStatus', DeferredPaymentStatus);
components.set('EstimatedBill', EstimatedBill);
components.set('ForecastedBill', ForecastedBill);
components.set('ProductWidget', ProductWidget);
components.set('DaytimePass', DaytimePass);
components.set('FreeEVMiles', FreeEVMiles);
components.set('LiveYourFree', LiveYourFree);
components.set('RenewalBuyBack', RenewalBuyBack);
components.set('SeasonPass', SeasonPass);
components.set('SolarBuybackMatch', SolarBuybackMatch);
components.set('UltimateSummerPass', UltimateSummerPass);
components.set('RenewMyPlan', RenewMyPlan);
components.set('RewardsWidget', RewardsWidget);
components.set('UsagetoDate', UsagetoDate);
components.set('WeeklyComparison', WeeklyComparison);
components.set('GreenupWidget', GreenupWidget);
components.set('ProductWidgetContainer', ProductWidgetContainer);
components.set('SummaryContainer', SummaryContainer);
components.set('TEEForgotPassword', TEEForgotPassword);
components.set('UsageBreakdown', UsageBreakdown);
components.set('UsageGraph', UsageGraph);
components.set('UsageOverviewContainer', UsageOverviewContainer);
components.set('CharityWidget', CharityWidget);
components.set('MyCharity', MyCharity);
components.set('types', types);
components.set('ComponentProps', ComponentProps);
components.set('CharityItem', CharityItem);
components.set('SelectedCharity', SelectedCharity);
components.set('myCharity', myCharity);
components.set('MyEnergyDashboardContainer', MyEnergyDashboardContainer);
components.set('CompareBill', CompareBill);
components.set('DailyUsageGraph', DailyUsageGraph);
components.set('HomeComparison', HomeComparison);
components.set('HourlyUsageGraph', HourlyUsageGraph);
components.set('MonthlyUsageGraph', MonthlyUsageGraph);
components.set('AddonPlanCard', AddonPlanCard);
components.set('ExtendedAddonPlanCard', ExtendedAddonPlanCard);
components.set('AddonPlanCardList', AddonPlanCardList);
components.set('MyPlan', MyPlan);
components.set('MyPlanAndProductsContainer', MyPlanAndProductsContainer);
components.set('MyPlansContainer', MyPlansContainer);
components.set('MyProducts', MyProducts);
components.set('MyReferral', MyReferral);
components.set('FreeEnergyRewards', FreeEnergyRewards);
components.set('NonCommodityProductPlanCard', NonCommodityProductPlanCard);
components.set('NonCommodityProducts', NonCommodityProducts);
components.set('NotificationBanner', NotificationBanner);
components.set('PageBanner', PageBanner);
components.set('AddBank', AddBank);
components.set('AddCard', AddCard);
components.set('BillPaymentHistory', BillPaymentHistory);
components.set('BillPaymentRewardsHistoryContainer', BillPaymentRewardsHistoryContainer);
components.set('CancelScheduledPayment', CancelScheduledPayment);
components.set('ConfirmCancelScheduledPayment', ConfirmCancelScheduledPayment);
components.set('RedeemRewards', RedeemRewards);
components.set('RewardsHistory', RewardsHistory);
components.set('AccordionList', AccordionList);
components.set('PaymentConfirmationInfo', PaymentConfirmationInfo);
components.set('ReviewPaymentInfo', ReviewPaymentInfo);
components.set('ChoosePaymentMethodContainer', ChoosePaymentMethodContainer);
components.set('DeferralConfirmation', DeferralConfirmation);
components.set('PickADate', PickADate);
components.set('ReviewDeferralRequest', ReviewDeferralRequest);
components.set('ReviewDeferralRequestAndPay', ReviewDeferralRequestAndPay);
components.set('PaymentConfirmation', PaymentConfirmation);
components.set('PaymentContainer', PaymentContainer);
components.set('PaymentDetails', PaymentDetails);
components.set('PaymentMethod', PaymentMethod);
components.set('ReviewPayment', ReviewPayment);
components.set('SplitPaymentAmount', SplitPaymentAmount);
components.set('SplitPaymentMethod', SplitPaymentMethod);
components.set('SplitPaymentSelectContainer', SplitPaymentSelectContainer);
components.set('PaymetricIntegration', PaymetricIntegration);
components.set('PdfViewer', PdfViewer);
components.set('ExtendedPlanCard', ExtendedPlanCard);
components.set('PlanCard', PlanCard);
components.set('RecentActivity', RecentActivity);
components.set('ReportOutage', ReportOutage);
components.set('RoutingTypeAhead', RoutingTypeAhead);
components.set('AddressDetails', AddressDetails);
components.set('Autopay', Autopay);
components.set('useAccountBalance', useAccountBalance);
components.set('AverageMonthlyBilling', AverageMonthlyBilling);
components.set('CommunicationPreferences', CommunicationPreferences);
components.set('CommunicationPreferenceToggle', CommunicationPreferenceToggle);
components.set('CustomerInformation', CustomerInformation);
components.set('EditBank', EditBank);
components.set('EditCard', EditCard);
components.set('LoginInformation', LoginInformation);
components.set('ManagePaymentMethods', ManagePaymentMethods);
components.set('PaperlessBilling', PaperlessBilling);
components.set('SettingsAndPreferencesContainer', SettingsAndPreferencesContainer);
components.set('Styleguide-ComponentParams', StyleguideComponentParams);
components.set('Styleguide-CustomRouteType', StyleguideCustomRouteType);
components.set('Styleguide-Layout-Reuse', StyleguideLayoutReuse);
components.set('Styleguide-Layout-Tabs-Tab', StyleguideLayoutTabsTab);
components.set('Styleguide-Layout-Tabs', StyleguideLayoutTabs);
components.set('Styleguide-Layout', StyleguideLayout);
components.set('Styleguide-Multilingual', StyleguideMultilingual);
components.set('Styleguide-RouteFields', StyleguideRouteFields);
components.set('Styleguide-Section', StyleguideSection);
components.set('Styleguide-SitecoreContext', StyleguideSitecoreContext);
components.set('Styleguide-Specimen', StyleguideSpecimen);
components.set('Styleguide-Tracking', StyleguideTracking);
components.set('SunRunPlanCard', SunRunPlanCard);
components.set('SunRunPlanCardList', SunRunPlanCardList);
components.set('NCPlanTermsText', NCPlanTermsText);
components.set('TermsAndConditionsContainer', TermsAndConditionsContainer);

export const componentBuilder = new ComponentBuilder({ components });

export const moduleFactory = componentBuilder.getModuleFactory();
