@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'MyriadPro-Regular';
  src: url('../../public/fonts/MyriadPro-Regular.otf');
}

@font-face {
  font-family: 'MyriadPro-Black';
  src: url('../../public/fonts/MyriadPro-Black.otf');
}

@font-face {
  font-family: 'MyriadPro-Light';
  src: url('../../public/fonts/MyriadPro-Light.otf');
}

@font-face {
  font-family: 'MyriadPro-Bold';
  src: url('../../public/fonts/MyriadPro-Bold.otf');
}

@font-face {
  font-family: 'MyriadPro-Semibold';
  src: url('../../public/fonts/MyriadPro-Semibold.otf');
}

@font-face {
  font-family: 'GothaPro-Regular';
  src: url('../../public/fonts/GothaProReg.otf');
}

@font-face {
  font-family: 'GothaPro-Medium';
  src: url('../../public/fonts/GothaProMed.otf');
}

@font-face {
  font-family: 'GothaPro-Bold';
  src: url('../../public/fonts/GothaProBol.otf');
}

@font-face {
  font-family: 'GothaPro-Light';
  src: url('../../public/fonts/GothaProLig.otf');
}

@font-face {
  font-family: 'GothaPro-Black';
  src: url('../../public/fonts/GothaProBla.otf');
}

@font-face {
  font-family: 'OpenSans-Regular';
  src: url('../../public/fonts/OpenSans-Regular.ttf');
}

@font-face {
  font-family: 'OpenSans-Bold';
  src: url('../../public/fonts/OpenSans-Bold.ttf');
}

@font-face {
  font-family: 'EnergyBold';
  src: url('../../public/fonts/EnergyBold.otf');
}

@layer components {
  .inline-link a {
    @apply text-textPrimary underline underline-offset-4 decoration-textPrimary hover:text-hoverSecondary focus:outline-none font-primaryBold;
  }
  .page-link a {
    @apply text-textSecondary decoration-textPrimary hover:text-hoverPrimary focus:outline-none font-primaryBold;
  }
  .dunning-header h4 {
    @apply text-textPrimary text-plus1 font-primaryBold lg:text-plus3;
  }
  .dunning-header p {
    @apply text-textQuattuordenary text-minus1 font-primaryRegular lg:text-plus1;
  }
  .dunning-disclaimer {
    @apply text-textQuattuordenary text-minus1 font-primaryRegular lg:text-plus1;
  }
  ul.deferral-list {
    @apply list-disc leading-6 pl-5;
  }
  ul.deferral-list li::marker {
    color: #326295;
    line-height: 3.5rem;
    font-size: 18px;
    font-family: 'MyriadPro-Regular';
  }
}

@layer utilities {
  .scrollbar::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar::-webkit-scrollbar:horizontal {
    display: none;
  }

  .scrollbar::-webkit-scrollbar-track {
    width: 8px;
    background-color: #d3d6dc;
    border-radius: 4px;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    width: 8px;
    border-radius: 4px;
    background-color: #326295;
  }

  .scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #326295 #d3d6dc;
  }
}

* {
  box-sizing: border-box;
}

body {
  overflow-x: hidden;
}

body {
  background-image: linear-gradient(
    15deg,
    rgba(0, 37, 84, 1) 41%,
    rgb(30, 27, 88) 77%,
    rgb(92, 30, 167) 100%
  );
  background-size: 100% 120px;
  background-repeat: no-repeat;
  @media (max-width: 767px) {
    background-size: 100% 145px;
  }
  @media only screen and (max-height: 820px) and (max-width: 1180px) {
    background-size: 100% 196px;
  }
}

/*
  Hides Sitecore editor markup,
  if you run the app in connected mode while the Sitecore cookies
  are set to edit mode.
*/
.scChromeData,
.scpm {
  display: none !important;
}

/*
  Styles for default JSS error components
*/
.sc-jss-editing-error,
.sc-jss-placeholder-error {
  padding: 1em;
  background-color: lightyellow;
}

/* 
  Style for default content block
*/

/*additional css*/

.list-disc-custom li:before {
  content: '';
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAOCAMAAAD6xte7AAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAADqUExURf////v+/eX48sjv4tHy5vf9+/r+/NXz6YrdwU/MoWLRq7zs29bz6XHWsxC5fwC0dgm3e0fJnfz+/tv17G3UsQW1eTHDkP7///z+/fn9/Pr+/f7//uP38WbSrg65fgG0dxm7g4PbvfP8+bPp13fYtpniye369uj482DRqgG0dhe7g3PXtZ/jzDPDkQ+4fhy8hV7Qqef48xS6gQK0d3jYt+v69X7ZuQy4fQK1dxW6guf48h+9h3vZuJ7jyyvBjVrPpyG+iH3Zut327d/27oLbvCK+iH7autr169n063vZuR29huT48X/ZujbEk8+QGbkAAAABYktHRACIBR1IAAAACXBIWXMAABJ0AAASdAHeZh94AAAAB3RJTUUH5wgHCDQg5GVwZQAAAAFvck5UAc+id5oAAAC7SURBVBjTVc/pVoJgEAbgydLSjDdzmRZatKxcWcxAqFiKXe7/duTjVEfm5zM7USUOaodH9So1jk+ardNqVftMwnlnny66PQnoD4j48uqaBd3It3fA/cOQaPT4NH4W9vIqTYDprNgxXywVVSPSVH0FKG/rIv1uwNyolqVuTMD+kMWczy/A1B1HL8j1/HL0908ArAyjaAzCqCTiOHRRhhvG/HsWR54tyPYi/r+V/SQF0sTnvQc42+b5NvujHb6HF3v8PSSRAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIzLTA4LTA3VDA4OjUxOjQ0KzAwOjAwdS8QugAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMy0wOC0wN1QwODo1MTo0NCswMDowMARyqAYAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjMtMDgtMDdUMDg6NTI6MzIrMDA6MDDRRQ75AAAAAElFTkSuQmCC');
  background-size: 10px 9px;
  position: absolute;
  left: -30px;
  top: 2px;
  width: 20px;
  height: 20px;
  border: 2px solid #00b476;
  border-radius: 100px;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}
.list-disc-custom ul {
  margin-left: 30px;
}
.list-disc-custom li {
  position: relative;
  margin: 20px 0px;
}

.svg-top {
  position: relative;
  top: 0;
  margin-top: -27%;
  margin-bottom: -40px;
}
.w-fit .rounded-lg {
  background: #fff;
}

.ml-temp-14 {
  padding-left: 150px;
  padding-right: 50px;
}
.w-full .flex.flex-col.rounded-xl.font-primaryRegular.text-charcoal-full {
  background: #fff;
}
.plan-space {
  margin-top: 180px;
}

.close .close-icon {
  position: absolute;
  right: 32px;
  top: 32px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  color: transparent;
}
.close .close-icon:hover {
  opacity: 1;
}
.close .close-icon:before,
.close .close-icon:after {
  position: absolute;
  left: 15px;
  content: ' ';
  height: 18px;
  width: 2.5px;
  background-color: #003f6e;
}
.close .close-icon:before {
  transform: rotate(45deg);
}
.close .close-icon:after {
  transform: rotate(-45deg);
}

.top-minus {
  top: -100px;
}

.embeddedServiceHelpButton .helpButton .uiButton {
  background-color: #326295 !important;
  font-family: 'Arial', sans-serif !important;
}

.embeddedServiceHelpButton .helpButton .uiButton:focus {
  outline: 1px solid #326295 !important;
}

.dialog-button-0 {
  display: none !important;
}
button.selected {
  background: #326295;
  color: white;
}

@media (max-width: 767px) {
  .case-manage {
    padding-left: 0px !important;
  }
  .selected-tooltip,
  .usage-tooltip,
  .billing-tooltip,
  .extended-plan-tooltip {
    margin-left: 0;
  }
  .billing-tooltip {
    --tw-translate-x: -80%;
  }
  .usage-tooltip {
    --tw-translate-x: -60%;
  }
  .extended-plan-tooltip {
    --tw-translate-x: -35%;
  }
  .selected-tooltip {
    --tw-translate-x: -30%;
  }
  .selected-tooltip.address-details-tooltip {
    --tw-translate-x: -65%;
  }
  .homecompare-tooltip .selected-tooltip {
    --tw-translate-x: -70%;
  }
  .selected-tooltip.plus-left {
    --tw-translate-x: -70%;
  }
  .addonplan-tooltip {
    --tw-translate-x: -80%;
  }
  .tee-box .mantine-InputWrapper-root {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .selected-Monthly,
  .selected-Hourly,
  .selected-Daily {
    margin-left: 170px;
  }
}
.tee-box .mantine-InputWrapper-root {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.tee-box .mantine-Radio-root,
.tee-box .mantine-Checkbox-root {
  border: 1px solid #326295;
  border-radius: 4px;
  background: #fff;
}
.tee-box [data-checked='true'] {
  background: #326295;
}
.tee-box [data-checked='true'] .tee-custom-color {
  color: #fff;
}
.tee-box input {
  border: none;
  background: none;
}
.tee-box .mantine-Checkbox-root input {
  border: 1px solid #ced4da;
  background: white;
}
.tee-box .mantine-Checkbox-root input:checked {
  border: 1px solid #ced4da;
  background: #326295;
}
.my-account-logout:after {
  content: '';
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAbCAYAAABvCO8sAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAH3SURBVEhLY/wPBAx0BExQmm6AIgsvPf/J8OMPaQFEtoVFm18z6Pc/YhBrvMfQtOcdVJQwIDsOGctuQ1kQ0OohzFDlJATl4QZk+zDBhA/KgoDqHW8Zug++h/JwA7ItLLEXZNAWZ4PyIKBs6xuGCYc/QHnYAdkWgixbGSOJYWkhMG6nHMNtKUWpFJeluRteM8w88RHKQwUUWQgCuCzNWPeKYe6pT1AeAlBsIQjgsjRlzUuGhWdRLaWKhSCAy9KElS8Zlp3/DOVB8+HyC58Z1lz6wvDu21+oMGXg9de/DFdf/oLyIADkmDA9HoiFYUueM6wGWkhLYCzDznAmTw4SpK+/UMdnxACwhfVuwgwOSpxgAVqBMgdIsUf1+jBg4XOGjVdRo2dppARDlCEvmE21VPrj938Gz7lPMSxbEC4OtwwEqGLh++//GLzmPWXYcfMbVAQC5oSIM8QboxbyFFv4/NMfBt/5zxj23/0OFYGAGUFiDMlmqJaBAEUW3n/3m8EfGGdHH6BaNjlAlCHdgh/KQwVkW3jj1S+GQKBlpx//gIpAQL+vKEOOlQCUhwnItrDzwHuGi8A2DTLo8hZhKLDFbRkIkG3hgjOohTKoiVEKrJQJAbItLETySSOw4CCmPQMCFGX8B+9/M/CxMzEIcTFDRQgDqpc0+AEDAwBhc7DRaOfs/gAAAABJRU5ErkJggg==');
  background-size: 20px 18px;
  position: relative;
  right: 0px;
  top: 10px;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}
.my-account-logout:hover:after {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAZCAYAAAAiwE4nAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAAHQSURBVEhL1ZZLKERRHId/7O1tlIVsbJSSkkwNMfKaQl4xoijPESNMYSGhPEKNjLDwFpE8ihBKJO8oeaS80iCvxGK4c/65NAY515RvM+f738V375k7c6+N8RVYEVv6tBrcwfX9Ezw+PZP9AGFLf4u6vt8I7wyjnSLXWNI6StOv4foObWSZtGKUJgWiINaX7HO4tlTl704rRqF+GJVdk2SfwxXMiZTDxdGejKHRDaGmd5rMHK6gEOsuTjCLqhsGUD8wS/YR7rvUUjS9tg+NQ/NkItxBAUvRlKoeNI8skDEkCQpYiiZVdKJtfJFMwqCApaiqrB0dE8um9dvvsHNyGX0za7i8uTcd4OXi+g5bh2dkjO4ilRiMKG5B7/Sq6cBf4ebsIG6pcEbW4C1YpFJA5upE9jdoouTilkpJqFaPwbkNMka7Ng7RPm7S3qXCY0qh0ZnFWvNjTDEByYJXtw8IyGvE2OI2TRh6TRTi/cQ/eUmCp4YbBOU3YWpllyYMXXYEEgM8yBjcwYNTA0IKmzC/uU8TRl1mGJKDPclEuII7R+dQapuxtHNEE0Z1qhJpSi+yj3AFyzsmsLZ3TMaoSAlGVriMzJz/9YqhfnclJQmKb2MA8AKgcghshFV5qgAAAABJRU5ErkJggg==');
}
.tee .mantine-Accordion-panel.tee-custom,
.tee .mantine-Accordion-panel.tee-custom > div {
  width: 100%;
}

.mantine-Select-input {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  -ms-appearance: none;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  -khtml-border-radius: 8px;
}
.tee .mantine-Select-input {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  -ms-appearance: none;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -khtml-border-radius: 4px;
}
@supports (-webkit-touch-callout: none) {
  /* ios device specific */
  .mantine-Select-input {
    border-radius: 8px;
  }
  .tee .mantine-Select-input {
    border-radius: 4px;
  }
}
.last a:last-child span {
  display: none;
}

.make-payment-btn a {
  color: #326295;
  font-family: 'MyriadPro-Bold';
}
.make-payment-btn a:hover {
  color: #003f6e;
}

.custom-victory-container {
  width: auto !important;
  height: auto !important;
}
.custom-victory-container svg {
  position: relative;
  overflow: visible;
}
.border-hide p + .border-b-charcoal-25,
.border-hide .border-b-charcoal-25:last-child {
  display: none !important;
}
.borderNone tr td {
  border: none !important;
}

.mp_payment_fields .mantine-TextInput-root,
.mp_payment_fields .mantine-DateInput-root {
  @apply max-w-[200px];
}
.mp_payment_fields .mantine-InputWrapper-label {
  @apply text-textUndenary font-primaryBold text-base mb-3;
}
.mp_payment_fields .mantine-Input-input {
  @apply text-textUndenary font-primaryBold text-plus1 sm:text-plus2 mb-5;
  border: 0;
  border-bottom: 2px solid #326295;
  border-radius: 0;
}
.ambit_modal .mantine-Modal-body {
  @apply p-0;
}

.amb-animation {
  transform: translate(-50%, -50%);
  animation: flipAnimation 2s infinite;
}

.amb-triangle-exclamation-blue {
  --fa-secondary-opacity: 1;
  --fa-primary-color: #ffffff;
  --fa-secondary-color: #326295;
}

@media (min-width: 1024px) {
  .large-screen-left {
    position: relative;
    left: calc(30px + 95px * var(--index));
  }
  .case-manage {
    padding-left: 80px !important;
  }
}

.header-amb {
  background-image: linear-gradient(
    27deg,
    rgba(0, 37, 84, 1) 41%,
    rgb(30, 27, 88) 77%,
    rgb(92, 30, 167) 100%
  );
}

table.disconnect-table {
  @apply table-auto text-left font-primaryRegular lg:mb-10 row-span-3 lg:row-auto;
}

table.disconnect-table > thead.hidden-xs {
  @apply collapse lg:visible font-primaryBold tracking-wide;
}
table.disconnect-table tr.hidden-xs {
  @apply collapse lg:visible border-b-0 lg:border-b-2 h-20;
}

tr.visible-xs {
  @apply col-span-3;
}

.tee-triangle-exclamation-yellow {
  --fa-secondary-opacity: 1;
  --fa-primary-color: #001e29;
  --fa-secondary-color: #ffb412;
}

.tee-triangle-exclamation-red {
  --fa-secondary-opacity: 1;
  --fa-primary-color: #ffffff;
  --fa-secondary-color: #d62334;
}

.tee input:checked + .mantine-Switch-track {
  background-color: #326295;
}

.tee .trieagle-animation {
  transform: translate(-50%, -50%);
  animation: flipAnimation 2s infinite;
}

@keyframes flipAnimation {
  0% {
    transform: rotate(0deg);
    fill: #1a2c56;
  }

  100% {
    transform: rotate(360deg);
    fill: #ff5733;
  }
}

#lightningContainer {
  margin-left: 360px;
  padding-left: 0 !important;
}
