import { faBuildingColumns, faCircleQuestion } from '@fortawesome/pro-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Radio, UnstyledButton } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { Field, LinkField, Text, withDatasourceCheck } from '@sitecore-jss/sitecore-jss-nextjs';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios-1.4';
import Button from 'components/Elements/Button/Button';
import { ComponentProps } from 'lib/component-props';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import aiLogger from 'src/hoc/ApplicationInsightsLogger';
import { useLoader } from 'src/hooks/modalhooks';
import { GetPaymentMethodsResponse } from 'src/services/MyAccountAPI/types';
import { getCardIcon } from 'src/utils/cardHelper';
import { PaymentDetailBankList, PaymentDetailCardList } from '../PaymentContainer/PaymentContainer';
import Loader from 'components/common/Loader/Loader';
import { useAppDispatch, useAppSelector } from 'src/stores/store';
import { isExpired, isExpiring } from 'src/utils/getCardExpiration';
import { setCardEditDetails } from 'src/stores/paymentSlice';
import { GetUserProfileResponse } from 'src/services/AuthenticationAPI/types';

type PaymentMethodProps = ComponentProps & {
  fields: {
    data: {
      item: {
        PaymentMethodsSubTitleText: Field<string>;
        AccountEndingText: Field<string>;
        CardEndingText: Field<string>;
        AddPaymentMethodText: Field<string>;
        AddCardBtnText: Field<string>;
        AddBankAccountBtnText: Field<string>;
        EnableSplitPayment: Field<boolean>;
        SplitPayment: Field<string>;
        ContinueToPaymentReview: Field<string>;
        CancelPaymentReview: Field<string>;
        AutoPayPaymentMethodTitleText: Field<string>;
        AllowOneTimePayment: Field<boolean>;
        SplitPaymentRedirectionLink?: { jsonValue: LinkField };
        InvalidExpirationDateErrorMessage: Field<string>;
        UpdateCardText: Field<string>;
        RedirectEditCardLink?: { jsonValue: LinkField };
        CardExpiringSoonErrorMessage: Field<string>;
        EditCardUpdateLink?: { jsonValue: LinkField };
        AutoPayEnableLabel?: { jsonValue: { value: string } };
      };
    };
  };
  isSplitPaymentAllowed?: boolean;
  openAddCardModal: () => void;
  openAddBankModal: () => void;
  isAutoPayPaymentMethod: boolean;
  isAutoPayOn: string;
  bankPaymentIndicator: string;
  cardPaymentIndicator: string;
  formField: string;
  form: UseFormReturnType<unknown>;
  setPaymentDetails: React.Dispatch<
    React.SetStateAction<PaymentDetailCardList | PaymentDetailBankList>
  >;
};

// function RadioIcon(props: { className: string; aria_hidden: boolean }): React.ReactSVGElement {
//   return React.createElement(
//     'svg',
//     {
//       xmlns: 'http://www.w3.org/2000/svg',
//       viewBox: '0 0 512 512',
//       style: {
//         width: 20,
//         height: 20,
//         transform: 'translate(-6px, -6px)',
//       },
//       className: props.className,
//     },
//     React.createElement('path', {
//       fill: '#0075DA',
//       d: 'M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z',
//     })
//   );
// }
function TickIcon(props: { className: string; aria_hidden: boolean }): React.ReactSVGElement {
  return React.createElement(
    'svg',
    {
      xmlns: 'http://www.w3.org/2000/svg',
      viewBox: '0 0 512 512',
      style: {
        width: 30,
        height: 30,
        transform: 'translate(-10px, -10px)',
      },
      className: props.className,
    },
    // Background Circle
    React.createElement('path', {
      fill: '#326295', // Background color
      d: 'M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z',
    }),
    // Orange Tick
    React.createElement('path', {
      fill: '#8dc63f', // Orange tick color
      d: 'M369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z',
    })
  );
}

const branding = process.env.NEXT_PUBLIC_SITE_NAME;
const PaymentMethod = ({
  isSplitPaymentAllowed = false,
  ...props
}: PaymentMethodProps): JSX.Element => {
  const [userProfileData, setUserProfileData] = useState<GetUserProfileResponse>();
  const dispatch = useAppDispatch();
  const selectedAccount = useAppSelector(
    (state) => state.authuser.accountSelection.contractAccount?.value
  );

  const { isLoading, isFetching, isRefetching, data, error } = useQuery({
    queryKey: ['paymentmethods', selectedAccount],
    queryFn: () =>
      axios
        .get<GetPaymentMethodsResponse>('/api/myaccount/payments/paymentmethods', {
          params: {
            accountNumber: selectedAccount,
          },
        })
        .then((res) => res.data),
    onSuccess: (onsuccessdata) => {
      const bankList = onsuccessdata.result.bankList;
      const cardList = onsuccessdata.result.cardList;
      if (cardList.length === 1 && bankList.length === 0) {
        props.form.setFieldValue(props.formField, cardList[0].AccountId);
        props.setPaymentDetails({ ...cardList[0], savePayment: false, valid: '' });
        props.form.setFieldValue('paymentByCard', 'Y');
      } else if (cardList.length === 0 && bankList.length === 1) {
        props.form.setFieldValue(props.formField, bankList[0].AccountId);
        props.setPaymentDetails({ ...bankList[0], savePayment: false });
        props.form.setFieldValue('paymentByCard', 'N');
      }
    },
  });
  const router = useRouter();
  const { openModal } = useLoader();
  // const oneTimePaymentCard = useAppSelector((state) => state.payment.oneTimePaymentDetails.card);
  // const oneTimePaymentBank = useAppSelector((state) => state.payment.oneTimePaymentDetails.bank);

  useEffect(() => {
    let getAutoPayAccount = '';
    if (
      !isLoading &&
      data?.result &&
      props.isAutoPayPaymentMethod === true &&
      props.isAutoPayOn === 'Y'
    ) {
      const getAutoPayAccountForBank = data?.result?.bankList
        .filter((x) => x.HasRecurringPayments)
        .map((x) => x.AccountId)[0];

      const getAutoPayAccountForCard = data?.result?.cardList
        .filter((x) => x.HasRecurringPayments)
        .map((x) => x.AccountId)[0];

      if (getAutoPayAccountForBank) getAutoPayAccount = getAutoPayAccountForBank;
      else if (getAutoPayAccountForCard) getAutoPayAccount = getAutoPayAccountForCard;

      props.form.setFieldValue('paymentMethod', getAutoPayAccount);
    } else {
      props.form.setFieldValue('paymentMethod', '');
    }
  }, [data, props.isAutoPayOn]);

  useEffect(() => {
    async function getUserProfileInfo() {
      const req = await axios
        .get<GetUserProfileResponse>('/api/userprofile')
        .then((res) => res.data);

      if (req) {
        console.log(req.result);
        if (req?.result?.isFraud) {
          console.log('Customer is Fraud!!');
          router.push('/oops-payment');
        }
        setUserProfileData(req);
      }
    }
    getUserProfileInfo();
  }, []);

  const Wrapper = 'div';

  if (isLoading || isFetching || isRefetching)
    return (
      <>
        <div>
          <div className="flex flex-col max-w-[1100px] h-64 rounded-xl shadow-none p-0">
            <Text
              tag="p"
              field={{
                value: props.isAutoPayPaymentMethod
                  ? props.fields.data?.item?.AutoPayPaymentMethodTitleText.value
                  : props.fields.data?.item?.PaymentMethodsSubTitleText.value,
              }}
              className="md:text-left md:text-plus2 text-textUndenary font-primaryBold mb-5 text-left"
            />
            <Loader />
          </div>
        </div>
      </>
    );

  if (error) return <></>;

  return (
    <div
      className={`mp_payment_method bg-gray-100 shadow-none p-5 rounded-lg border-t-[1px] sm:border-t-0 border-borderPrimary 
    ${props.isAutoPayPaymentMethod ? 'sm:w-4/5' : 'w-full'}`}
    >
      <Text
        tag="h2"
        field={{
          value: props.isAutoPayPaymentMethod
            ? props.fields.data?.item?.AutoPayPaymentMethodTitleText.value
            : props.fields.data?.item?.PaymentMethodsSubTitleText.value,
        }}
        className="md:text-left sm:mt-3 md:text-plus2 sm:pt-3  text-textUndenary font-primaryBold sm:mb-3 text-left"
      />
      <hr className="w-full border-1 border-borderQuattuordenary my-4 hidden" />
      <div className="tee-box amb_payment_method">
        <Radio.Group
          styles={{
            root: {
              display: 'flex',
              flexDirection: 'column',
              gap: '18px',
              width: '100%',
              marginTop: '18px',
              marginBottom: '18px',
              cursor: 'pointer',
              [`@media (min-width: 768px)`]: {
                gap: '23px',
                marginTop: '23px',
                marginBottom: '23px',
              },
            },
          }}
          {...props.form.getInputProps(props.formField)}
        >
          {props.isAutoPayPaymentMethod && props.bankPaymentIndicator !== 'Y' ? (
            <></>
          ) : (
            data?.result.bankList.map((bank) => (
              <>
                <Radio
                  className={`${bank.HasRecurringPayments ? '!max-h-[90px]' : ''}`}
                  key={bank.AccountId}
                  labelPosition="left"
                  value={bank.AccountId}
                  onClick={() => {
                    if (props.setPaymentDetails && typeof props.setPaymentDetails === 'function') {
                      props.setPaymentDetails({ ...bank, savePayment: false });
                      props.form.setFieldValue('paymentByCard', 'N');
                    }
                  }}
                  icon={TickIcon}
                  label={
                    <div>
                      <div className="flex relative  left-4 flex-row w-full gap-3 items-center cursor-pointer ml-0">
                        <div className="w-[36px] h-[28px] flex items-center justify-center rounded-[4px] bg-bgSecondary">
                          <FontAwesomeIcon icon={faBuildingColumns} className="text-textQuinary" />
                        </div>
                        <div className="flex flex-col md:flex-col sm:items-start md:w-4/5 cursor-pointer py-2">
                          <Text //Show TXU and Hide TEE
                            className="tee-custom-color sm:text-base sm:w-[510px] w-[240px] md:pr-2 font-primaryBold text-minus2 text-textUndenary text-left"
                            tag="p"
                            field={{ value: bank.Nickname }}
                          />
                          <Text
                            className="tee-custom-color text-left text-textQuattuordenary text-xs md:ml-0 md:text-sm"
                            tag="p"
                            field={{
                              value: props.fields.data?.item?.AccountEndingText.value.replace(
                                '{AccountNumber}',
                                `${bank.DisplayAccountNumber}`
                              ),
                            }}
                          ></Text>
                        </div>
                      </div>
                      {bank.HasRecurringPayments && (
                        <Text
                          className="tee-custom-color relative  -top-1.5 left-4 font-primaryBold text-textPrimary text-xs md:ml-0 md:text-sm pt-1"
                          tag="p"
                          field={{
                            value: props.fields?.data?.item?.AutoPayEnableLabel?.jsonValue?.value,
                          }}
                        ></Text>
                      )}
                    </div>
                  }
                  styles={{
                    root: {
                      maxHeight: '68px',
                    },
                    inner: {
                      transform: 'translate(-20px, 70%)',
                    },
                    radio: {
                      border: 'none', // Remove border if isAmb is true
                    },
                    labelWrapper: {
                      display: 'flex',
                      overflow: 'hidden',
                    },
                    body: {
                      width: '100%',
                    },
                  }}
                />
              </>
            ))
          )}
          {/* {props.fields.AllowOneTimePayment?.value && oneTimePaymentBank && (
            <>
              <Radio
                labelPosition="left"
                value={oneTimePaymentBank.accountNumber}
                onClick={() => {
                  if (props.setPaymentDetails && typeof props.setPaymentDetails === 'function') {
                    // props.setPaymentDetails(bank);
                    const bankDetails: BankList = {
                      DefaultPaymentMethod: false,
                      RoutingNumber: oneTimePaymentBank.routingNumber,
                      bankAcountType: 0,
                      InternationalBankAccount: null,
                      BankCountryKey: '',
                      ChangeCategory: null,
                      HasRecurringPayments: false,
                      HasScheduledPayments: false,
                      RecurringContractAccountNumber: [],
                      HoldersName: oneTimePaymentBank.holderName,
                      Nickname: oneTimePaymentBank.holderName,
                      Description: oneTimePaymentBank.holderName,
                      AccountId: oneTimePaymentBank.accountNumber,
                      DisplayAccountNumber: oneTimePaymentBank.accountNumber.slice(-4),
                      profileId: '',
                      paymentType: 1,
                    };
                    props.setPaymentDetails({
                      ...bankDetails,
                      savePayment: oneTimePaymentBank.savePayment,
                    });
                    props.form.setFieldValue('paymentByCard', 'N');
                  }
                }}
             icon={isAmb ? TickIcon : RadioIcon}

                label={
                  <div className="flex flex-row w-full gap-3 items-center ml-1 cursor-pointer">
                    <div className="w-[36px] h-[28px] flex items-center justify-center rounded-[4px] bg-txublue">
                      <FontAwesomeIcon icon={faBuildingColumns} className="text-white" />
                    </div>
                    <div className="flex flex-col md:flex-row md:items-center ml-2 md:w-4/5 cursor-pointer">
                      <Text
                        className="font-mProBlack text-sm text-txublue md:text-lg md:w-[174px] tee:hidden"
                        tag="p"
                        field={{ value: oneTimePaymentBank.holderName }}
                      />
                      <Text
                        className="tee-custom-color text-charcoal-full text-xs tee:text-tee-txtgrey tee:font-gProBold tee:ml-0 md:text-sm"
                        tag="p"
                        field={{
                          value: props.fields.AccountEndingText.value.replace(
                            '{AccountNumber}',
                            `${oneTimePaymentBank.accountNumber.slice(-4)}`
                          ),
                        }}
                      ></Text>
                    </div>
                  </div>
                }
                styles={{
                  inner: {
                    transform: 'translate(0px, 6px)',
                  },
                  labelWrapper: {
                    width: '100%',
                  },
                }}
              />
              <hr className="w-full border-1 border-charcoal-50 tee:hidden" />
            </>
          )} */}
          {props.isAutoPayPaymentMethod && props.cardPaymentIndicator !== 'Y' ? (
            <></>
          ) : (
            data?.result.cardList.map((card) => (
              <Wrapper key={card.AccountId}>
                <Radio
                  className={`${card.HasRecurringPayments ? '!max-h-[90px]' : ''}`}
                  key={card.AccountId}
                  labelPosition="left"
                  value={card.AccountId}
                  onClick={() => {
                    if (props.setPaymentDetails && typeof props.setPaymentDetails === 'function') {
                      props.setPaymentDetails({ ...card, savePayment: false, valid: '' });
                      props.form.setFieldValue('paymentByCard', 'Y');
                    }
                  }}
                  disabled={isExpired(card.Expiration)}
                  icon={TickIcon}
                  label={
                    <div>
                      <div className="flex ml-3 flex-row w-full gap-3 items-center cursor-pointer overflow-hidden">
                        <FontAwesomeIcon
                          icon={getCardIcon(card.AccountId)}
                          className="h-[32px] text-textSecondary w-auto tee-custom-color"
                        />
                        <div className="flex flex-col md:flex-col md:items-start overflow-hidden md:w-4/5 py-2">
                          <Text
                            className="tee-custom-color md:text-lg sm:w-[500px] w-[230px] md:pr-2 font-primaryBold text-minus2 text-textUndenary whitespace-nowrap text-left"
                            tag="p"
                            field={{ value: card.Nickname }}
                          />
                          <Text
                            className="tee-custom-color text-textQuattuordenary text-xs md:ml-0 md:text-sm text-left"
                            tag="p"
                            field={{
                              value: props.fields.data?.item?.CardEndingText.value.replace(
                                '{CardNumber}',
                                `${card.DisplayAccountNumber}`
                              ),
                            }}
                          />
                        </div>
                      </div>
                      {card.HasRecurringPayments && (
                        <Text
                          className="tee-custom-color relative  -top-1.5 left-4 font-primaryBold text-textPrimary text-xs md:ml-0 md:text-sm pt-1"
                          tag="p"
                          field={{
                            value: props.fields?.data?.item?.AutoPayEnableLabel?.jsonValue?.value,
                          }}
                        ></Text>
                      )}
                    </div>
                  }
                  styles={{
                    root: {
                      maxHeight: '68px',
                    },
                    inner: {
                      transform: 'translate(-20px, 70%)',
                    },
                    radio: {
                      border: 'none', // Remove border if isAmb is true
                    },
                    labelWrapper: {
                      display: 'flex',
                      overflow: 'hidden',
                    },
                    body: {
                      width: '100%',
                    },
                  }}
                />
                {/* Check if the card is expired */}
                {isExpired(card.Expiration) && (
                  <div>
                    <div>
                      <div>
                        <p className="font-primaryRegular mt-2 text-minus2">
                          <span className="text-textDenary">
                            {props.fields?.data?.item?.InvalidExpirationDateErrorMessage?.value}
                          </span>
                          <UnstyledButton
                            className="font-primaryBold text-textPrimary text-minus2"
                            onClick={() => {
                              dispatch(
                                setCardEditDetails({
                                  cardholderName: card.HoldersName,
                                  cardDisplayNumber: card.DisplayAccountNumber,
                                  expiration: card.Expiration,
                                  zipCode: card.BillingPostalCode,
                                  nickname: card.Nickname,
                                  accountId: card.AccountId,
                                  profileId: card.profileId,
                                  hasSchedulePayments: card.HasScheduledPayments,
                                  hasRecurringPayments: card.HasRecurringPayments,
                                  cardBrand: card.CardBrand,
                                })
                              );
                              router.push({
                                pathname:
                                  props.fields.data?.item?.EditCardUpdateLink?.jsonValue?.value
                                    .href,
                              });
                            }}
                          >
                            {props.fields?.data?.item?.UpdateCardText?.value}
                          </UnstyledButton>
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Check if the card is isExpiring */}
                {isExpiring(card.Expiration) && (
                  <div className="flex flex-row w-full gap-3 items-center">
                    <div>
                      <div>
                        <p className="font-primaryRegular mt-2 text-minus2">
                          <span className="text-textDenary">
                            {props.fields?.data?.item?.CardExpiringSoonErrorMessage?.value}{' '}
                          </span>
                          <UnstyledButton
                            className="font-primaryBold text-textPrimary text-minus2"
                            onClick={() => {
                              dispatch(
                                setCardEditDetails({
                                  cardholderName: card.HoldersName,
                                  cardDisplayNumber: card.DisplayAccountNumber,
                                  expiration: card.Expiration,
                                  zipCode: card.BillingPostalCode,
                                  nickname: card.Nickname,
                                  accountId: card.AccountId,
                                  profileId: card.profileId,
                                  hasSchedulePayments: card.HasScheduledPayments,
                                  hasRecurringPayments: card.HasRecurringPayments,
                                  cardBrand: card.CardBrand,
                                })
                              );
                              router.push({
                                pathname:
                                  props.fields.data?.item?.EditCardUpdateLink?.jsonValue?.value
                                    .href,
                              });
                            }}
                          >
                            {props.fields?.data?.item?.UpdateCardText?.value}
                          </UnstyledButton>
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {branding !== 'amb' ? (
                  <hr className="w-full border-1 border-borderQuattuordenary" />
                ) : null}
              </Wrapper>
            ))
          )}
          {/* {props.fields.AllowOneTimePayment?.value && oneTimePaymentCard && (
            <>
              <Radio
                value={oneTimePaymentCard.cardNumber}
                onClick={() => {
                  if (props.setPaymentDetails && typeof props.setPaymentDetails === 'function') {
                    const cardDetails: CardList = {
                      DefaultPaymentMethod: false,
                      Expiration: oneTimePaymentCard.expiration,
                      BillingPostalCode: oneTimePaymentCard.zip,
                      CardType: 0,
                      PayAgent: '',
                      PayChannel: 'web',
                      HasRecurringPayments: false,
                      HasScheduledPayments: false,
                      RecurringContractAccountNumber: [],
                      HoldersName: oneTimePaymentCard.holderName,
                      Nickname: oneTimePaymentCard.nickname,
                      Description: '',
                      AccountId: oneTimePaymentCard.cardNumber,
                      DisplayAccountNumber: oneTimePaymentCard.cardNumber.slice(
                        oneTimePaymentCard.cardNumber.length - 8,
                        oneTimePaymentCard.cardNumber.length - 4
                      ),
                      profileId: '',
                      paymentType: 0,
                    };
                    props.setPaymentDetails({
                      ...cardDetails,
                      savePayment: oneTimePaymentCard.savePayment,
                      valid: oneTimePaymentCard.valid,
                    });
                  }
                  props.form.setFieldValue('paymentByCard', 'Y');
                }}
                labelPosition="left"
             icon={isAmb ? TickIcon : RadioIcon}

                label={
                  <div className="flex flex-row w-full gap-3 items-center cursor-pointer">
                    <FontAwesomeIcon
                      icon={getCardIcon(oneTimePaymentCard.cardNumber)}
                      className="w-[48px] h-[32px] text-txublue"
                    />
                    <div className="flex flex-col md:flex-row md:items-center md:w-4/5">
                      <Text
                        className="font-mProBlack text-sm text-txublue md:text-lg md:w-[174px] tee:hidden"
                        tag="p"
                        field={{ value: oneTimePaymentCard.nickname }}
                      />
                      <Text
                        className="tee-custom-color text-charcoal-full text-xs md:ml-auto tee:ml-0 tee:text-tee-txtgrey tee:font-gProBold md:text-sm"
                        tag="p"
                        field={{
                          value: props.fields.CardEndingText.value.replace(
                            '{CardNumber}',
                            `${oneTimePaymentCard.cardNumber.slice(
                              oneTimePaymentCard.cardNumber.length - 8,
                              oneTimePaymentCard.cardNumber.length - 4
                            )}`
                          ),
                        }}
                      />
                    </div>
                  </div>
                }
                styles={{
                  inner: {
                    transform: 'translate(0px, 6px)',
                  },
                  labelWrapper: {
                    width: '100%',
                  },
                }}
              />
              <hr className="w-full border-1 border-charcoal-50 tee:hidden" />
            </>
          )} */}
        </Radio.Group>
      </div>
      <div className="payment-method !py-2">
        <h3 className="my-4 text-textUndenary font-primaryBold text-base">
          {props.fields.data?.item?.AddPaymentMethodText.value}
        </h3>
        <div className="flex flex-col gap-4 md:flex-row">
          {props.isAutoPayPaymentMethod && props.cardPaymentIndicator !== 'Y' ? (
            <></>
          ) : (
            <Button
              className="w-auto sm:min-w-[179px] sm:px-4"
              variant="secondary"
              size="small"
              type="button"
              onClick={() => {
                console.log('Opening add card modal');
                props.openAddCardModal();
              }}
            >
              {props.fields.data?.item?.AddCardBtnText.value}
            </Button>
          )}
          {userProfileData &&
            userProfileData?.result &&
            !userProfileData?.result?.isCashOnly &&
            !userProfileData?.result?.isFraud &&
            (props.isAutoPayPaymentMethod && props.bankPaymentIndicator !== 'Y' ? (
              <></>
            ) : (
              <Button
                className="w-auto sm:min-w-[179px] sm:px-4"
                variant="secondary"
                size="small"
                type="button"
                onClick={() => props.openAddBankModal()}
              >
                {props.fields.data?.item?.AddBankAccountBtnText.value}
              </Button>
            ))}
        </div>

        {props.fields.data?.item?.EnableSplitPayment.value &&
          !props.isAutoPayPaymentMethod &&
          isSplitPaymentAllowed && (
            <>
              <Button
                className="text-minus2 text-textSecondary hover:text-textprimary font-primaryBold p-0"
                variant="borderless"
                size="small"
                type="button"
                onClick={() => {
                  openModal();
                  router.push(
                    props.fields?.data?.item?.SplitPaymentRedirectionLink?.jsonValue?.value
                      ?.href as string
                  );
                }}
              >
                <FontAwesomeIcon icon={faCircleQuestion} />
                {props.fields.data?.item?.SplitPayment.value}
              </Button>
            </>
          )}
      </div>
    </div>
  );
};

export { PaymentMethod };
const Component = withDatasourceCheck()<PaymentMethodProps>(PaymentMethod);
export default aiLogger(Component, Component.name);
